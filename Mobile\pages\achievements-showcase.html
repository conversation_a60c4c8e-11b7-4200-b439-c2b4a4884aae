<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>成果展示 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .showcase-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .stats-overview {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 11px;
            opacity: 0.8;
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: #c00714;
            color: white;
        }

        .showcase-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .showcase-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .showcase-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .showcase-item.excellent {
            border-left: 4px solid #28a745;
        }

        .showcase-item.good {
            border-left: 4px solid #007bff;
        }

        .showcase-item.normal {
            border-left: 4px solid #ffc107;
        }

        .showcase-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            flex-shrink: 0;
        }

        .showcase-info {
            flex: 1;
        }

        .student-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .student-class {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .showcase-title {
            font-size: 14px;
            color: #333;
            line-height: 1.4;
        }

        .showcase-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
        }

        .showcase-badge.excellent {
            background: #d4edda;
            color: #155724;
        }

        .showcase-badge.good {
            background: #cce5ff;
            color: #004085;
        }

        .showcase-badge.normal {
            background: #fff3cd;
            color: #856404;
        }

        .showcase-content {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-top: 12px;
            font-size: 13px;
            color: #666;
            line-height: 1.5;
            max-height: 60px;
            overflow: hidden;
            position: relative;
        }

        .showcase-content::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 20px;
            background: linear-gradient(transparent, #f8f9fa);
        }

        .showcase-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12px;
            font-size: 12px;
            color: #999;
        }

        .showcase-time {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .showcase-stats {
            display: flex;
            gap: 12px;
        }

        .stat-icon {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">成果展示</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="showcase-container">
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalWorks">0</div>
                    <div class="stat-label">作品总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="excellentWorks">0</div>
                    <div class="stat-label">优秀作品</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalViews">0</div>
                    <div class="stat-label">总浏览量</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalLikes">0</div>
                    <div class="stat-label">总点赞数</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all">全部</button>
                <button class="filter-tab" data-filter="excellent">优秀</button>
                <button class="filter-tab" data-filter="good">良好</button>
                <button class="filter-tab" data-filter="normal">一般</button>
            </div>
        </div>

        <!-- 成果列表 -->
        <div class="showcase-list" id="showcaseList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载成果展示...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentFilter = 'all';
        let showcaseData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载成果展示数据
            loadShowcaseData();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentFilter = filter;
                    renderShowcase();
                });
            });
        }

        function loadShowcaseData() {
            // 获取学生作品/成果数据
            $.ajax({
                url: baseurl + "/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 100,
                    categoryId: '', // 可以按分类筛选
                    creator: '' // 可以按创建者筛选
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        showcaseData = (res.data.list || []).map(item => ({
                            id: item.id,
                            title: item.title,
                            content: item.content,
                            studentName: item.creator?.name || '匿名用户',
                            studentClass: item.creator?.className || '未知班级',
                            level: getWorkLevel(item),
                            views: item.views || 0,
                            likes: item.likes || 0,
                            createdAt: item.createdAt,
                            category: item.category?.name || '其他'
                        }));
                        
                        updateStatsOverview();
                        renderShowcase();
                    } else {
                        // 如果没有数据，加载模拟数据
                        loadMockShowcaseData();
                    }
                },
                error: (err) => {
                    console.error('加载成果展示失败:', err);
                    // 加载模拟数据
                    loadMockShowcaseData();
                }
            });
        }

        function loadMockShowcaseData() {
            // 模拟成果展示数据
            showcaseData = [
                {
                    id: '1',
                    title: '学习马克思主义基本原理的心得体会',
                    content: '通过深入学习马克思主义基本原理，我深刻认识到马克思主义是科学的世界观和方法论...',
                    studentName: '张三',
                    studentClass: '计算机科学与技术2021级1班',
                    level: 'excellent',
                    views: 156,
                    likes: 23,
                    createdAt: new Date().toISOString(),
                    category: '学习心得'
                },
                {
                    id: '2',
                    title: '红色经典阅读感悟',
                    content: '阅读《红星照耀中国》后，我对中国共产党的光辉历程有了更深入的了解...',
                    studentName: '李四',
                    studentClass: '软件工程2021级2班',
                    level: 'good',
                    views: 89,
                    likes: 15,
                    createdAt: new Date(Date.now() - 86400000).toISOString(),
                    category: '读书笔记'
                },
                {
                    id: '3',
                    title: '参观革命纪念馆有感',
                    content: '今天参观了革命纪念馆，看到了许多珍贵的历史文物和资料，深受震撼...',
                    studentName: '王五',
                    studentClass: '信息安全2021级1班',
                    level: 'good',
                    views: 67,
                    likes: 12,
                    createdAt: new Date(Date.now() - 172800000).toISOString(),
                    category: '实践感悟'
                },
                {
                    id: '4',
                    title: '新时代青年的责任与担当',
                    content: '作为新时代的青年，我们肩负着实现中华民族伟大复兴的历史使命...',
                    studentName: '赵六',
                    studentClass: '网络工程2021级1班',
                    level: 'excellent',
                    views: 134,
                    likes: 28,
                    createdAt: new Date(Date.now() - 259200000).toISOString(),
                    category: '思想感悟'
                }
            ];
            
            updateStatsOverview();
            renderShowcase();
        }

        function getWorkLevel(work) {
            // 根据浏览量和点赞数判断作品等级
            const score = (work.views || 0) + (work.likes || 0) * 5;
            if (score >= 100) return 'excellent';
            if (score >= 50) return 'good';
            return 'normal';
        }

        function updateStatsOverview() {
            const totalWorks = showcaseData.length;
            const excellentWorks = showcaseData.filter(item => item.level === 'excellent').length;
            const totalViews = showcaseData.reduce((sum, item) => sum + (item.views || 0), 0);
            const totalLikes = showcaseData.reduce((sum, item) => sum + (item.likes || 0), 0);

            document.getElementById('totalWorks').textContent = totalWorks;
            document.getElementById('excellentWorks').textContent = excellentWorks;
            document.getElementById('totalViews').textContent = totalViews;
            document.getElementById('totalLikes').textContent = totalLikes;
        }

        function renderShowcase() {
            const showcaseList = document.getElementById('showcaseList');
            
            let filteredData = showcaseData;
            if (currentFilter !== 'all') {
                filteredData = showcaseData.filter(item => item.level === currentFilter);
            }
            
            if (filteredData.length === 0) {
                showcaseList.innerHTML = '<div class="empty-state"><div class="empty-icon">🏆</div><div>暂无相关成果</div></div>';
                return;
            }

            let html = '';
            filteredData.forEach(item => {
                const levelText = getLevelText(item.level);
                
                html += `
                    <div class="showcase-item ${item.level}" onclick="viewShowcaseDetail('${item.id}')">
                        <div class="showcase-badge ${item.level}">${levelText}</div>
                        <div class="showcase-header">
                            <div class="student-avatar">${item.studentName ? item.studentName.charAt(0) : 'S'}</div>
                            <div class="showcase-info">
                                <div class="student-name">${item.studentName}</div>
                                <div class="student-class">${item.studentClass}</div>
                                <div class="showcase-title">${item.title}</div>
                            </div>
                        </div>
                        <div class="showcase-content">
                            ${item.content || '暂无内容'}
                        </div>
                        <div class="showcase-meta">
                            <div class="showcase-time">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17L8 12L9.41 10.59L12 13.17L14.59 10.59L16 12L13 17Z"/>
                                </svg>
                                ${MobileUtils.formatTime(item.createdAt)}
                            </div>
                            <div class="showcase-stats">
                                <div class="stat-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5C17 19.5 21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12C7 9.24 9.24 7 12 7C14.76 7 17 9.24 17 12C17 14.76 14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12C15 10.34 13.66 9 12 9Z"/>
                                    </svg>
                                    ${item.views || 0}
                                </div>
                                <div class="stat-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                        <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z"/>
                                    </svg>
                                    ${item.likes || 0}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            showcaseList.innerHTML = html;
        }

        function getLevelText(level) {
            const levelMap = {
                'excellent': '优秀',
                'good': '良好',
                'normal': '一般'
            };
            return levelMap[level] || '未知';
        }

        function viewShowcaseDetail(showcaseId) {
            // 跳转到成果详情页
            window.location.href = `showcase-detail.html?id=${showcaseId}`;
        }
    </script>
</body>
</html>
