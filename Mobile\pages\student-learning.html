<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习统计 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .learning-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .stats-overview {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }

        .filter-select {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            font-size: 13px;
            background: #f8f9fa;
        }

        .filter-select:focus {
            outline: none;
            border-color: #c00714;
            background: white;
        }

        .chart-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            text-align: center;
        }

        .chart-container {
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8f9fa;
            border-radius: 8px;
            color: #666;
        }

        .category-stats {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 16px;
        }

        .category-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            text-align: center;
        }

        .category-name {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .category-count {
            font-size: 18px;
            font-weight: 600;
            color: #c00714;
        }

        .time-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 16px;
        }

        .time-item {
            text-align: center;
            flex: 1;
        }

        .time-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }

        .time-label {
            font-size: 12px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .refresh-btn {
            background: #c00714;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .refresh-btn:active {
            transform: scale(0.95);
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">学习统计</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="learning-container">
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalStudents">0</div>
                    <div class="stat-label">学生总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="totalHours">0</div>
                    <div class="stat-label">总学习时长</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgHours">0</div>
                    <div class="stat-label">平均时长</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-row">
                <select class="filter-select" id="collegeSelect">
                    <option value="">全部学院</option>
                </select>
                <select class="filter-select" id="majorSelect">
                    <option value="">全部专业</option>
                </select>
            </div>
            <div class="filter-row">
                <select class="filter-select" id="classSelect">
                    <option value="">全部班级</option>
                </select>
                <select class="filter-select" id="subjectSelect">
                    <option value="">全部学科</option>
                </select>
            </div>
            <div class="filter-row">
                <input type="date" class="filter-select" id="startDate">
                <input type="date" class="filter-select" id="endDate">
                <button class="refresh-btn" onclick="loadLearningStats()">刷新</button>
            </div>
        </div>

        <!-- 分类访问统计 -->
        <div class="chart-section">
            <div class="chart-title">分类访问统计</div>
            <div class="chart-container" id="categoryChart">
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>正在加载统计数据...</p>
                </div>
            </div>
            <div class="category-stats" id="categoryStats"></div>
        </div>

        <!-- 学习时间统计 -->
        <div class="chart-section">
            <div class="chart-title">学习时间分布</div>
            <div class="chart-container" id="timeChart">
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>正在加载时间统计...</p>
                </div>
            </div>
            <div class="time-stats" id="timeStats"></div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentFilters = {
            collegeId: '',
            majorId: '',
            classId: '',
            subjectId: '',
            startDate: '',
            endDate: ''
        };

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选器
            initFilters();
            
            // 加载学习统计数据
            loadLearningStats();
        });

        function initFilters() {
            // 设置默认日期范围（最近30天）
            const endDate = new Date();
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - 30);
            
            document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
            
            // 加载筛选选项
            loadFilterOptions();
            
            // 绑定筛选器事件
            document.getElementById('collegeSelect').addEventListener('change', onFilterChange);
            document.getElementById('majorSelect').addEventListener('change', onFilterChange);
            document.getElementById('classSelect').addEventListener('change', onFilterChange);
            document.getElementById('subjectSelect').addEventListener('change', onFilterChange);
            document.getElementById('startDate').addEventListener('change', onFilterChange);
            document.getElementById('endDate').addEventListener('change', onFilterChange);
        }

        function loadFilterOptions() {
            // 加载学院列表
            $.ajax({
                url: baseurl + "/college/major",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                success: (res) => {
                    if (res.code == '200') {
                        populateCollegeSelect(res.data || []);
                    }
                },
                error: (err) => {
                    console.error('加载学院列表失败:', err);
                }
            });

            // 加载学科列表
            $.ajax({
                url: baseurl + "/subject/listall",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                success: (res) => {
                    if (res.code == '200') {
                        populateSubjectSelect(res.data || []);
                    }
                },
                error: (err) => {
                    console.error('加载学科列表失败:', err);
                }
            });
        }

        function populateCollegeSelect(colleges) {
            const select = document.getElementById('collegeSelect');
            colleges.forEach(college => {
                const option = document.createElement('option');
                option.value = college.id;
                option.textContent = college.name;
                select.appendChild(option);
            });
        }

        function populateSubjectSelect(subjects) {
            const select = document.getElementById('subjectSelect');
            subjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.id;
                option.textContent = subject.name;
                select.appendChild(option);
            });
        }

        function onFilterChange() {
            currentFilters = {
                collegeId: document.getElementById('collegeSelect').value,
                majorId: document.getElementById('majorSelect').value,
                classId: document.getElementById('classSelect').value,
                subjectId: document.getElementById('subjectSelect').value,
                startDate: document.getElementById('startDate').value,
                endDate: document.getElementById('endDate').value
            };
            
            // 延迟加载，避免频繁请求
            clearTimeout(window.filterTimeout);
            window.filterTimeout = setTimeout(() => {
                loadLearningStats();
            }, 500);
        }

        function loadLearningStats() {
            // 获取最新任务ID
            $.ajax({
                url: baseurl + "/task/latest",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                success: (res) => {
                    const taskId = res.code == '200' ? res.data?.id : null;
                    loadCategoryStats(taskId);
                    loadTimeStats(taskId);
                },
                error: (err) => {
                    console.error('获取最新任务失败:', err);
                    loadCategoryStats(null);
                    loadTimeStats(null);
                }
            });
        }

        function loadCategoryStats(taskId) {
            const params = {
                collegeId: currentFilters.collegeId,
                majorId: currentFilters.majorId,
                classId: currentFilters.classId,
                subjectId: currentFilters.subjectId,
                time1: currentFilters.startDate,
                time2: currentFilters.endDate,
                taskId: taskId
            };

            $.ajax({
                url: baseurl + "/stat/numCategory",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: params,
                success: (res) => {
                    if (res.code == '200') {
                        renderCategoryStats(res.data || []);
                    } else {
                        // 如果没有数据，加载模拟数据
                        loadMockCategoryStats();
                    }
                },
                error: (err) => {
                    console.error('加载分类统计失败:', err);
                    // 加载模拟数据
                    loadMockCategoryStats();
                }
            });
        }

        function loadTimeStats(taskId) {
            const params = {
                ...currentFilters,
                time1: currentFilters.startDate,
                time2: currentFilters.endDate,
                taskId: taskId
            };

            $.ajax({
                url: baseurl + "/stat/timeTrend",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: params,
                success: (res) => {
                    if (res.code == '200') {
                        renderTimeStats(res.data || []);
                    } else {
                        renderEmptyTimeStats();
                    }
                },
                error: (err) => {
                    console.error('加载时间统计失败:', err);
                    // 加载模拟数据
                    loadMockTimeStats();
                }
            });
        }

        function renderCategoryStats(data) {
            const chartContainer = document.getElementById('categoryChart');
            const statsContainer = document.getElementById('categoryStats');
            
            if (data.length === 0) {
                renderEmptyCategoryStats();
                return;
            }

            // 简单的图表显示
            chartContainer.innerHTML = '<div style="color: #c00714; font-weight: 600;">分类访问数据已加载</div>';
            
            // 渲染分类统计
            let html = '';
            let totalStudents = 0;
            let totalVisits = 0;

            data.forEach(item => {
                totalVisits += item.count || 0;
                html += `
                    <div class="category-item">
                        <div class="category-name">${item.name || '未知分类'}</div>
                        <div class="category-count">${item.count || 0}</div>
                    </div>
                `;
            });

            statsContainer.innerHTML = html;
            
            // 更新概览统计
            document.getElementById('totalStudents').textContent = data.length;
            document.getElementById('totalHours').textContent = Math.round(totalVisits / 60) + 'h';
            document.getElementById('avgHours').textContent = data.length > 0 ? Math.round(totalVisits / data.length / 60) + 'h' : '0h';
        }

        function renderTimeStats(data) {
            const chartContainer = document.getElementById('timeChart');
            const statsContainer = document.getElementById('timeStats');
            
            if (data.length === 0) {
                renderEmptyTimeStats();
                return;
            }

            // 简单的图表显示
            chartContainer.innerHTML = '<div style="color: #c00714; font-weight: 600;">时间分布数据已加载</div>';
            
            // 计算时间统计
            let morningCount = 0;
            let afternoonCount = 0;
            let eveningCount = 0;

            data.forEach(item => {
                const hour = new Date(item.date).getHours();
                if (hour >= 6 && hour < 12) {
                    morningCount += item.count || 0;
                } else if (hour >= 12 && hour < 18) {
                    afternoonCount += item.count || 0;
                } else {
                    eveningCount += item.count || 0;
                }
            });

            statsContainer.innerHTML = `
                <div class="time-item">
                    <div class="time-value">${morningCount}</div>
                    <div class="time-label">上午</div>
                </div>
                <div class="time-item">
                    <div class="time-value">${afternoonCount}</div>
                    <div class="time-label">下午</div>
                </div>
                <div class="time-item">
                    <div class="time-value">${eveningCount}</div>
                    <div class="time-label">晚上</div>
                </div>
            `;
        }

        function renderEmptyCategoryStats() {
            document.getElementById('categoryChart').innerHTML = '<div class="empty-state"><div class="empty-icon">📊</div><div>暂无分类统计数据</div></div>';
            document.getElementById('categoryStats').innerHTML = '';
        }

        function loadMockCategoryStats() {
            // 模拟分类统计数据
            const mockData = [
                { name: '课程学习', count: 156 },
                { name: '红色书籍', count: 89 },
                { name: 'VR红色游学', count: 67 },
                { name: '虚拟仿真实验', count: 45 },
                { name: '医德博物馆', count: 34 },
                { name: '总书记足迹', count: 23 }
            ];
            renderCategoryStats(mockData);
        }

        function loadMockTimeStats() {
            // 模拟时间统计数据
            const mockData = [];
            const now = new Date();
            for (let i = 6; i >= 0; i--) {
                const date = new Date(now);
                date.setDate(date.getDate() - i);
                mockData.push({
                    date: date.toISOString(),
                    count: Math.floor(Math.random() * 50) + 10
                });
            }
            renderTimeStats(mockData);
        }

        function renderEmptyTimeStats() {
            document.getElementById('timeChart').innerHTML = '<div class="empty-state"><div class="empty-icon">⏰</div><div>暂无时间统计数据</div></div>';
            document.getElementById('timeStats').innerHTML = '';
        }
    </script>
</body>
</html>
