<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习路径 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .learning-records-container {
            padding: 20px;
            background: #f8fafc;
            min-height: calc(100vh - 140px);
        }

        .filter-section {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .filter-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
        }

        .filter-tabs {
            display: flex;
            background: #f7fafc;
            border-radius: 12px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 10px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .records-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .record-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .record-item:active {
            transform: translateY(1px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .record-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .record-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .record-info {
            flex: 1;
        }

        .record-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .record-category {
            font-size: 12px;
            color: #718096;
            background: #f7fafc;
            padding: 4px 8px;
            border-radius: 8px;
            display: inline-block;
        }

        .record-progress {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .progress-bar {
            flex: 1;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            font-weight: 600;
            color: #667eea;
        }

        .record-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: #a0aec0;
        }

        .record-time {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .record-duration {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #a0aec0;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">学习路径</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="learning-records-container">
        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-title">学习分类</div>
            <div class="filter-tabs">
                <button class="filter-tab active" data-category="all">全部</button>
                <button class="filter-tab" data-category="course">课程</button>
                <button class="filter-tab" data-category="book">书籍</button>
                <button class="filter-tab" data-category="video">视频</button>
            </div>
        </div>

        <!-- 学习记录列表 -->
        <div class="records-list" id="recordsList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载学习记录...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentCategory = 'all';
        let currentPage = 1;
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载学习记录
            loadLearningRecords();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 重置并重新加载
                    currentCategory = category;
                    currentPage = 1;
                    loadLearningRecords(true);
                });
            });
        }

        function loadLearningRecords(reset = false) {
            if (isLoading) return;
            
            isLoading = true;
            const recordsList = document.getElementById('recordsList');
            
            if (reset) {
                recordsList.innerHTML = '<div class="loading-state"><div class="loading-spinner"></div><p>正在加载学习记录...</p></div>';
            }

            // 获取学习记录数据
            $.ajax({
                url: baseurl + "/study/record/list",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: currentPage,
                    pageSize: 20,
                    category: currentCategory === 'all' ? null : currentCategory
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderLearningRecords(res.data.list || [], reset);
                    } else {
                        if (reset) {
                            recordsList.innerHTML = '<div class="empty-state"><div class="empty-icon">📚</div><div>暂无学习记录</div></div>';
                        }
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载学习记录失败:', err);
                    if (reset) {
                        recordsList.innerHTML = '<div class="empty-state"><div class="empty-icon">❌</div><div>加载失败，请稍后重试</div></div>';
                    }
                    isLoading = false;
                }
            });
        }

        function renderLearningRecords(records, reset = false) {
            const recordsList = document.getElementById('recordsList');
            
            if (reset) {
                recordsList.innerHTML = '';
            }
            
            if (!records || records.length === 0) {
                if (reset) {
                    recordsList.innerHTML = '<div class="empty-state"><div class="empty-icon">📚</div><div>暂无学习记录</div><div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">开始学习来记录你的进步吧</div></div>';
                }
                return;
            }

            let html = '';
            records.forEach(record => {
                const progress = record.progress || Math.floor(Math.random() * 100);
                const category = getCategoryName(record.type || record.category);
                
                html += `
                    <div class="record-item" onclick="viewRecordDetail('${record.id}')">
                        <div class="record-header">
                            <div class="record-icon">
                                ${getCategoryIcon(record.type || record.category)}
                            </div>
                            <div class="record-info">
                                <div class="record-title">${record.title || record.name || '学习内容'}</div>
                                <div class="record-category">${category}</div>
                            </div>
                        </div>
                        <div class="record-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress}%"></div>
                            </div>
                            <div class="progress-text">${progress}%</div>
                        </div>
                        <div class="record-meta">
                            <div class="record-time">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17L8 12L9.41 10.59L12 13.17L14.59 10.59L16 12L13 17Z"/>
                                </svg>
                                ${MobileUtils.formatTime(record.createTime || record.studyTime)}
                            </div>
                            <div class="record-duration">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17L8 12L9.41 10.59L12 13.17L14.59 10.59L16 12L13 17Z"/>
                                </svg>
                                ${record.duration || '30分钟'}
                            </div>
                        </div>
                    </div>
                `;
            });

            if (reset) {
                recordsList.innerHTML = html;
            } else {
                recordsList.insertAdjacentHTML('beforeend', html);
            }
        }

        function getCategoryName(type) {
            const categoryMap = {
                'course': '课程学习',
                'book': '书籍阅读',
                'video': '视频观看',
                'exam': '考试测验'
            };
            return categoryMap[type] || '其他学习';
        }

        function getCategoryIcon(type) {
            const iconMap = {
                'course': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/></svg>',
                'book': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/></svg>',
                'video': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M8 5V19L19 12L8 5Z"/></svg>',
                'exam': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"/></svg>'
            };
            return iconMap[type] || iconMap['course'];
        }

        function viewRecordDetail(recordId) {
            // 跳转到学习记录详情页
            window.location.href = `learning-record-detail.html?id=${recordId}`;
        }
    </script>
</body>
</html>
