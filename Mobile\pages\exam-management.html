<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>考试管理 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .exam-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .create-exam-btn {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            width: 100%;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .create-exam-btn:active {
            transform: scale(0.98);
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: #c00714;
            color: white;
        }

        .exam-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .exam-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .exam-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .exam-item.draft {
            border-left: 4px solid #ffc107;
        }

        .exam-item.published {
            border-left: 4px solid #28a745;
        }

        .exam-item.ended {
            border-left: 4px solid #6c757d;
        }

        .exam-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .exam-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .exam-info {
            flex: 1;
        }

        .exam-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .exam-desc {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        .exam-status {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            position: absolute;
            top: 12px;
            right: 12px;
        }

        .exam-status.draft {
            background: #fff3cd;
            color: #856404;
        }

        .exam-status.published {
            background: #d4edda;
            color: #155724;
        }

        .exam-status.ended {
            background: #e2e3e5;
            color: #383d41;
        }

        .exam-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            font-size: 12px;
            margin-top: 12px;
        }

        .meta-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .meta-value {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .meta-label {
            color: #666;
        }

        .exam-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #c00714;
            color: white;
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">考试管理</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="exam-container">
        <!-- 创建考试按钮 -->
        <button class="create-exam-btn" onclick="createExam()">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
            创建新考试
        </button>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="draft">草稿</button>
                <button class="filter-tab" data-status="published">已发布</button>
                <button class="filter-tab" data-status="ended">已结束</button>
            </div>
        </div>

        <!-- 考试列表 -->
        <div class="exam-list" id="examList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载考试列表...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentStatus = 'all';
        let examsData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载考试列表
            loadExams();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const status = this.getAttribute('data-status');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentStatus = status;
                    renderExams();
                });
            });
        }

        function loadExams() {
            // 获取考试列表数据 - 使用PC端相同的接口
            $.ajax({
                url: baseurl + "/cms-test-paper/weblist",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 100
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        // 转换PC端数据格式为移动端需要的格式
                        examsData = (res.data.list || []).map(item => ({
                            id: item.id,
                            title: item.name,
                            name: item.name,
                            description: item.description || '暂无描述',
                            questionCount: item.questionCount || 0,
                            duration: item.duration || 60,
                            participantCount: item.participantCount || 0,
                            status: getExamStatus(item),
                            createdAt: item.createdAt,
                            startTime: item.startTime,
                            endTime: item.endTime
                        }));
                        renderExams();
                    } else {
                        document.getElementById('examList').innerHTML = '<div class="empty-state"><div class="empty-icon">📝</div><div>暂无考试</div><div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">点击上方按钮创建新考试</div></div>';
                    }
                },
                error: (err) => {
                    console.error('加载考试列表失败:', err);
                    document.getElementById('examList').innerHTML = '<div class="empty-state"><div class="empty-icon">❌</div><div>加载失败，请稍后重试</div></div>';
                }
            });
        }

        function getExamStatus(exam) {
            const now = new Date();
            const startTime = exam.startTime ? new Date(exam.startTime) : null;
            const endTime = exam.endTime ? new Date(exam.endTime) : null;

            if (!startTime || !endTime) {
                return 'draft';
            }

            if (now < startTime) {
                return 'published';
            } else if (now >= startTime && now <= endTime) {
                return 'published';
            } else {
                return 'ended';
            }
        }

        function renderExams() {
            const examList = document.getElementById('examList');
            
            let filteredExams = examsData;
            if (currentStatus !== 'all') {
                filteredExams = examsData.filter(exam => exam.status === currentStatus);
            }
            
            if (filteredExams.length === 0) {
                examList.innerHTML = '<div class="empty-state"><div class="empty-icon">📝</div><div>暂无相关考试</div><div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">切换筛选条件查看其他考试</div></div>';
                return;
            }

            let html = '';
            filteredExams.forEach(exam => {
                const statusText = getStatusText(exam.status);
                
                html += `
                    <div class="exam-item ${exam.status}" onclick="viewExamDetail('${exam.id}')">
                        <div class="exam-status ${exam.status}">${statusText}</div>
                        <div class="exam-header">
                            <div class="exam-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                                    <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"/>
                                </svg>
                            </div>
                            <div class="exam-info">
                                <div class="exam-title">${exam.title || exam.name}</div>
                                <div class="exam-desc">${exam.description || '暂无描述'}</div>
                            </div>
                        </div>
                        <div class="exam-meta">
                            <div class="meta-item">
                                <div class="meta-value">${exam.questionCount || 0}</div>
                                <div class="meta-label">题目数</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-value">${exam.duration || 60}分钟</div>
                                <div class="meta-label">考试时长</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-value">${exam.participantCount || 0}</div>
                                <div class="meta-label">参与人数</div>
                            </div>
                        </div>
                        <div class="exam-actions">
                            ${getActionButtons(exam)}
                        </div>
                    </div>
                `;
            });

            examList.innerHTML = html;
        }

        function getStatusText(status) {
            const statusMap = {
                'draft': '草稿',
                'published': '已发布',
                'ended': '已结束'
            };
            return statusMap[status] || '未知';
        }

        function getActionButtons(exam) {
            switch (exam.status) {
                case 'draft':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); editExam('${exam.id}')">编辑</button>
                        <button class="action-btn primary" onclick="event.stopPropagation(); publishExam('${exam.id}')">发布</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); deleteExam('${exam.id}')">删除</button>
                    `;
                case 'published':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); viewResults('${exam.id}')">查看结果</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); endExam('${exam.id}')">结束考试</button>
                    `;
                case 'ended':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); viewResults('${exam.id}')">查看结果</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); exportResults('${exam.id}')">导出结果</button>
                    `;
                default:
                    return '';
            }
        }

        function createExam() {
            MobileUtils.showToast('创建考试功能开发中');
        }

        function viewExamDetail(examId) {
            window.location.href = `exam-detail.html?id=${examId}`;
        }

        function editExam(examId) {
            MobileUtils.showToast('编辑考试功能开发中');
        }

        function publishExam(examId) {
            if (confirm('确定要发布这个考试吗？发布后将无法修改题目。')) {
                MobileUtils.showToast('发布考试功能开发中');
            }
        }

        function deleteExam(examId) {
            if (confirm('确定要删除这个考试吗？此操作无法撤销。')) {
                MobileUtils.showToast('删除考试功能开发中');
            }
        }

        function endExam(examId) {
            if (confirm('确定要结束这个考试吗？结束后学生将无法继续答题。')) {
                MobileUtils.showToast('结束考试功能开发中');
            }
        }

        function viewResults(examId) {
            window.location.href = `exam-results.html?id=${examId}`;
        }

        function exportResults(examId) {
            MobileUtils.showToast('导出结果功能开发中');
        }
    </script>
</body>
</html>
