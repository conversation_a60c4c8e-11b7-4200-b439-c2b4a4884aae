<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程数据结构测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .api-section {
            margin-bottom: 30px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #c00714;
        }
        
        .api-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .api-url {
            background: #e9ecef;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 14px;
            margin-bottom: 12px;
            word-break: break-all;
        }
        
        .data-structure {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
        }
        
        .data-title {
            font-weight: 600;
            color: #495057;
            margin-bottom: 8px;
        }
        
        .data-example {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .test-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 8px 8px 8px 0;
        }
        
        .test-btn:hover {
            background: #a00610;
        }
        
        .result-area {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin-top: 12px;
            min-height: 100px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            margin: 8px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-title">📊 课程数据结构测试</div>
        
        <div class="api-section">
            <div class="api-title">🔗 API接口信息</div>
            <div class="api-url">GET /project/section/list/tree</div>
            
            <div class="data-structure">
                <div class="data-title">预期数据结构：</div>
                <div class="data-example">{
  "code": "200",
  "data": [
    {
      "id": "course1",
      "name": "思想道德与法治",  // 这个作为课程名称
      "level": 0,
      "children": [
        {
          "id": "section1-1",
          "name": "第一章 人生的青春之问",
          "level": 1
        },
        {
          "id": "section1-2", 
          "name": "第二章 坚定理想信念",
          "level": 1
        }
      ]
    }
  ]
}</div>
            </div>
            
            <button class="test-btn" onclick="testAPI()">测试API接口</button>
            <button class="test-btn" onclick="testDataExtraction()">测试数据提取</button>
            <button class="test-btn" onclick="testCourseSelection()">测试课程选择</button>
            
            <div id="testResult" class="result-area">点击按钮开始测试...</div>
        </div>
        
        <div class="api-section">
            <div class="api-title">🎯 功能测试</div>
            
            <div class="data-structure">
                <div class="data-title">课程选择器模拟：</div>
                <select id="mockCourseSelect" style="width: 100%; padding: 8px; margin: 8px 0;">
                    <option value="all">全部课程</option>
                </select>
                
                <div class="data-title">章节选择器模拟：</div>
                <select id="mockSectionSelect" style="width: 100%; padding: 8px; margin: 8px 0;" disabled>
                    <option value="all">全部章节</option>
                </select>
            </div>
            
            <div id="selectionStatus" class="status info">
                请选择课程以测试联动功能
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="pages/course-list.html" class="test-btn">🚀 返回课程列表页面</a>
        </div>
    </div>
    
    <script>
        // 模拟的API数据
        const mockApiData = {
            "code": "200",
            "data": [
                {
                    "id": "course1",
                    "name": "思想道德与法治",
                    "level": 0,
                    "children": [
                        { "id": "section1-1", "name": "第一章 人生的青春之问", "level": 1 },
                        { "id": "section1-2", "name": "第二章 坚定理想信念", "level": 1 },
                        { "id": "section1-3", "name": "第三章 弘扬中国精神", "level": 1 }
                    ]
                },
                {
                    "id": "course2", 
                    "name": "中国近现代史纲要",
                    "level": 0,
                    "children": [
                        { "id": "section2-1", "name": "第一章 反对外国侵略的斗争", "level": 1 },
                        { "id": "section2-2", "name": "第二章 对国家出路的早期探索", "level": 1 },
                        { "id": "section2-3", "name": "第三章 辛亥革命与君主专制制度的终结", "level": 1 }
                    ]
                },
                {
                    "id": "course3",
                    "name": "马克思主义基本原理",
                    "level": 0,
                    "children": [
                        { "id": "section3-1", "name": "第一章 马克思主义是关于无产阶级和人类解放的科学", "level": 1 },
                        { "id": "section3-2", "name": "第二章 世界的物质性及发展规律", "level": 1 },
                        { "id": "section3-3", "name": "第三章 实践与认识及其发展规律", "level": 1 }
                    ]
                }
            ]
        };
        
        // 测试API接口
        function testAPI() {
            const resultArea = document.getElementById('testResult');
            resultArea.textContent = '正在测试API接口...\n';
            
            // 模拟API调用
            setTimeout(() => {
                resultArea.textContent += '✅ API接口测试成功\n';
                resultArea.textContent += '📊 返回数据结构：\n';
                resultArea.textContent += JSON.stringify(mockApiData, null, 2);
            }, 1000);
        }
        
        // 测试数据提取
        function testDataExtraction() {
            const resultArea = document.getElementById('testResult');
            resultArea.textContent = '正在测试数据提取...\n';
            
            // 模拟数据提取过程
            const courses = extractCoursesFromSectionTree(mockApiData.data);
            
            resultArea.textContent += '✅ 数据提取成功\n';
            resultArea.textContent += '📋 提取的课程列表：\n';
            courses.forEach((course, index) => {
                resultArea.textContent += `${index + 1}. ${course.title} (ID: ${course.id})\n`;
                if (course.children && course.children.length > 0) {
                    resultArea.textContent += `   章节数量: ${course.children.length}\n`;
                }
            });
            
            // 更新模拟选择器
            updateMockCourseSelect(courses);
        }
        
        // 测试课程选择
        function testCourseSelection() {
            const resultArea = document.getElementById('testResult');
            resultArea.textContent = '正在测试课程选择功能...\n';
            
            const courses = extractCoursesFromSectionTree(mockApiData.data);
            updateMockCourseSelect(courses);
            
            resultArea.textContent += '✅ 课程选择器已更新\n';
            resultArea.textContent += '🔗 请在上方选择器中测试联动功能\n';
        }
        
        // 从章节树数据中提取课程信息
        function extractCoursesFromSectionTree(sectionTreeData) {
            const courses = [];
            
            sectionTreeData.forEach((rootSection, index) => {
                if (rootSection.name) {
                    courses.push({
                        id: rootSection.id || `course_${index}`,
                        title: rootSection.name,
                        sectionId: rootSection.id,
                        category: 'course',
                        level: rootSection.level || 0,
                        children: rootSection.children || []
                    });
                }
            });
            
            return courses;
        }
        
        // 更新模拟课程选择器
        function updateMockCourseSelect(courses) {
            const courseSelect = document.getElementById('mockCourseSelect');
            let optionsHtml = '<option value="all">全部课程</option>';
            
            courses.forEach(course => {
                optionsHtml += `<option value="${course.id}">${course.title}</option>`;
            });
            
            courseSelect.innerHTML = optionsHtml;
        }
        
        // 模拟课程选择事件
        document.getElementById('mockCourseSelect').addEventListener('change', function() {
            const selectedCourseId = this.value;
            const sectionSelect = document.getElementById('mockSectionSelect');
            const statusDiv = document.getElementById('selectionStatus');
            
            if (selectedCourseId === 'all') {
                sectionSelect.disabled = true;
                sectionSelect.innerHTML = '<option value="all">全部章节</option>';
                statusDiv.textContent = '请选择课程以测试联动功能';
                statusDiv.className = 'status info';
            } else {
                sectionSelect.disabled = false;
                
                // 查找对应课程的章节
                const selectedCourse = mockApiData.data.find(course => course.id === selectedCourseId);
                if (selectedCourse && selectedCourse.children) {
                    let optionsHtml = '<option value="all">全部章节</option>';
                    selectedCourse.children.forEach(section => {
                        optionsHtml += `<option value="${section.id}">${section.name}</option>`;
                    });
                    sectionSelect.innerHTML = optionsHtml;
                    
                    statusDiv.textContent = `✅ 已选择课程：${selectedCourse.name}，章节选择器已启用`;
                    statusDiv.className = 'status success';
                } else {
                    statusDiv.textContent = '❌ 未找到对应课程的章节数据';
                    statusDiv.className = 'status error';
                }
            }
        });
        
        // 初始化测试
        document.addEventListener('DOMContentLoaded', function() {
            testDataExtraction();
        });
    </script>
</body>
</html>
