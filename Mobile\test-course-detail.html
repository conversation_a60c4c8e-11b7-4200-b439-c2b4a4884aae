<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程详情测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            text-align: center;
            color: #c00714;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-item h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .test-item p {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .test-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .test-btn:hover {
            background: #a00610;
        }
        
        .status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">课程详情页面测试</h1>
        
        <div class="test-item">
            <h3>🎨 1. 界面优化测试</h3>
            <p>检查播放按钮是否更精致，红色卡片是否更清爽简洁。</p>
            <a href="pages/course-detail.html?id=test1" class="test-btn" target="_blank">测试界面优化</a>
            <div class="status success">
                ✓ 播放按钮更小巧精致，卡片设计更清爽
            </div>
        </div>
        
        <div class="test-item">
            <h3>⏱ 2. 持续计时功能测试</h3>
            <p>验证学习计时持续运行不会过早停止，只在真正离开页面时才提交记录。</p>
            <a href="pages/course-detail.html?id=test2" class="test-btn" target="_blank">测试持续计时</a>
            <div class="status success">
                ✓ 修复了1秒停止问题，计时会持续到真正离开页面
            </div>
        </div>
        
        <div class="test-item">
            <h3>🎥 3. 视频全屏功能测试</h3>
            <p>检查视频播放器是否有全屏按钮，以及播放/暂停按钮是否在视频中央。</p>
            <a href="pages/course-detail.html?id=test3" class="test-btn" target="_blank">测试视频功能</a>
            <div class="status info">
                ℹ 视频控制条右侧应有全屏按钮，视频中央有大型播放/暂停按钮
            </div>
        </div>
        
        <div class="test-item">
            <h3>🎥 4. MP4视频自动播放测试</h3>
            <p>验证MP4视频文件在页面打开后是否自动播放。</p>
            <a href="pages/course-detail.html?id=test-video" class="test-btn" target="_blank">测试视频自动播放</a>
            <div class="status success">
                ✓ 添加了autoplay、muted、playsinline属性<br>
                ✓ 实现了autoPlayVideo()函数和错误处理<br>
                ✓ 显示自动播放状态提示
            </div>
        </div>

        <div class="test-item">
            <h3>📄 5. Word文档打开测试</h3>
            <p>测试DOC/DOCX文件的多种打开方式。</p>
            <a href="pages/course-detail.html?id=test-doc" class="test-btn" target="_blank">测试Word文档</a>
            <div class="status success">
                ✓ 在线查看器打开 (Office Online)<br>
                ✓ 微信内置浏览器打开<br>
                ✓ 下载到本地查看<br>
                ✓ 备用Google Docs查看器
            </div>
        </div>

        <div class="test-item">
            <h3>📊 6. PowerPoint演示文稿测试</h3>
            <p>测试PPT/PPTX文件的多种打开方式。</p>
            <a href="pages/course-detail.html?id=test-ppt" class="test-btn" target="_blank">测试PPT演示文稿</a>
            <div class="status success">
                ✓ 在线查看器打开 (Office Online)<br>
                ✓ 微信内置浏览器打开<br>
                ✓ 下载到本地查看<br>
                ✓ 备用Google Docs查看器
            </div>
        </div>

        <div class="test-item">
            <h3>🎵 7. MP3音频文件测试</h3>
            <p>测试MP3音频文件的播放功能。</p>
            <a href="pages/course-detail.html?id=test-audio" class="test-btn" target="_blank">测试音频播放</a>
            <div class="status success">
                ✓ 内置音频播放器<br>
                ✓ 播放/暂停控制<br>
                ✓ 下载功能<br>
                ✓ 播放错误处理
            </div>
        </div>

        <div class="test-item">
            <h3>📊 8. 学习记录提交修复测试</h3>
            <p>验证修复后的学习记录提交功能，解决API 500错误和过早停止问题。</p>
            <button class="test-btn" onclick="checkLocalStorage()">检查本地记录</button>
            <div id="recordStatus" class="status info">
                ℹ 点击按钮查看本地存储的学习记录
            </div>
            <div class="status success" style="margin-top: 10px;">
                ✓ 修复了API 500错误，使用异步提交和备用API机制<br>
                ✓ 修复了1秒停止问题，计时持续到真正离开页面
            </div>
        </div>
    </div>

    <script>
        function checkLocalStorage() {
            const records = localStorage.getItem('jilu');
            const statusDiv = document.getElementById('recordStatus');
            
            if (records) {
                const parsedRecords = JSON.parse(records);
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✓ 找到 ${parsedRecords.length} 条学习记录<br>最新记录: ${parsedRecords[parsedRecords.length - 1]?.timestamp || '无'}`;
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = 'ℹ 暂无本地学习记录，请先访问课程详情页面';
            }
        }
        
        // 页面加载时检查
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
