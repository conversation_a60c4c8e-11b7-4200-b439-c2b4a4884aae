<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选功能测试指南</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 30px;
            text-align: center;
            border-bottom: 2px solid #c00714;
            padding-bottom: 15px;
        }
        
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #c00714;
        }
        
        .section h3 {
            margin: 0 0 15px 0;
            font-size: 18px;
            color: #333;
        }
        
        .test-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .test-steps li {
            padding: 10px 0;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #e9ecef;
            position: relative;
            padding-left: 30px;
        }
        
        .test-steps li:last-child {
            border-bottom: none;
        }
        
        .test-steps li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 10px;
            background: #c00714;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .test-steps {
            counter-reset: step-counter;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 8px 8px 8px 0;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: monospace;
            font-size: 13px;
            color: #495057;
            display: inline-block;
            margin: 4px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-warning { background: #ffc107; }
        .status-error { background: #dc3545; }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">🧪 筛选功能测试指南</div>
        
        <div class="section">
            <h3>🔧 修复内容说明</h3>
            <div class="highlight success">
                <strong>✅ 已完成的修复：</strong>
                <ul>
                    <li>将移动端筛选逻辑改为使用PC端相同的API调用方式</li>
                    <li>修复了应用筛选后不显示结果的问题</li>
                    <li>优化了筛选参数的映射关系</li>
                    <li>增强了错误处理和调试信息</li>
                    <li>统一了所有筛选操作的处理方式</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h3>🧪 测试步骤</h3>
            
            <h4>基础功能测试</h4>
            <ol class="test-steps">
                <li>打开课程列表页面，等待页面加载完成</li>
                <li>检查是否显示课程卡片（应该有内容显示）</li>
                <li>打开浏览器开发者工具（F12），切换到Console标签</li>
                <li>观察控制台是否有错误信息</li>
            </ol>
            
            <h4>快速分类测试</h4>
            <ol class="test-steps">
                <li>点击不同的快速分类标签（全部、数学资源等）</li>
                <li>观察页面是否显示加载提示</li>
                <li>检查课程列表是否根据分类变化</li>
                <li>在控制台查看API请求和响应</li>
            </ol>
            
            <h4>筛选面板测试</h4>
            <ol class="test-steps">
                <li>点击筛选按钮打开筛选面板</li>
                <li>选择不同的课程选项</li>
                <li>观察章节选择器是否相应启用</li>
                <li>选择章节、类型、排序等选项</li>
                <li>点击"应用筛选"按钮</li>
                <li>检查是否显示筛选结果</li>
            </ol>
            
            <h4>重置功能测试</h4>
            <ol class="test-steps">
                <li>设置一些筛选条件</li>
                <li>点击"重置筛选"按钮</li>
                <li>检查所有筛选条件是否重置为默认值</li>
                <li>检查课程列表是否恢复为全部课程</li>
            </ol>
        </div>
        
        <div class="section">
            <h3>🔍 调试检查点</h3>
            
            <div class="highlight">
                <strong>在控制台中查找以下关键日志：</strong>
                <ul>
                    <li><span class="code">发送筛选请求，参数: {...}</span> - 筛选请求参数</li>
                    <li><span class="code">筛选API响应: {...}</span> - API响应数据</li>
                    <li><span class="code">筛选结果课程数量: X</span> - 返回的课程数量</li>
                    <li><span class="code">筛选完成，找到 X 个课程</span> - 最终结果提示</li>
                </ul>
            </div>
            
            <div class="highlight warning">
                <strong>⚠️ 如果仍然没有结果，检查：</strong>
                <ul>
                    <li>API是否返回了数据（检查响应中的data字段）</li>
                    <li>筛选参数是否正确（对比PC端的参数格式）</li>
                    <li>是否有网络错误或认证问题</li>
                    <li>课程数据的字段结构是否符合预期</li>
                </ul>
            </div>
        </div>
        
        <div class="section">
            <h3>🚨 常见问题解决</h3>
            
            <div style="margin-bottom: 20px;">
                <span class="status-indicator status-error"></span>
                <strong>问题：筛选后仍然没有结果</strong>
                <div style="margin-left: 20px; margin-top: 8px;">
                    <strong>可能原因：</strong>
                    <ul>
                        <li>API返回的数据为空</li>
                        <li>筛选条件过于严格</li>
                        <li>参数映射不正确</li>
                    </ul>
                    <strong>解决方法：</strong>
                    <ul>
                        <li>检查控制台中的API响应数据</li>
                        <li>尝试重置筛选条件</li>
                        <li>对比PC端的请求参数</li>
                    </ul>
                </div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <span class="status-indicator status-warning"></span>
                <strong>问题：加载提示一直显示</strong>
                <div style="margin-left: 20px; margin-top: 8px;">
                    <strong>可能原因：</strong>
                    <ul>
                        <li>API请求失败</li>
                        <li>网络连接问题</li>
                        <li>认证信息过期</li>
                    </ul>
                    <strong>解决方法：</strong>
                    <ul>
                        <li>检查网络连接</li>
                        <li>重新登录获取认证信息</li>
                        <li>查看控制台错误信息</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🛠️ 调试工具</h3>
            <button class="btn" onclick="openCoursePage()">打开课程列表页面</button>
            <button class="btn btn-secondary" onclick="openConsole()">打开控制台</button>
            <button class="btn btn-secondary" onclick="checkStatus()">检查状态</button>
        </div>
        
        <div class="footer">
            <p><strong>修复版本</strong>: v2.0 - 基于PC端API的筛选功能</p>
            <p><strong>更新时间</strong>: 2025-06-19</p>
            <p>如果问题仍然存在，请提供控制台的详细日志信息</p>
        </div>
    </div>
    
    <script>
        function openCoursePage() {
            window.open('pages/course-list.html', '_blank');
        }
        
        function openConsole() {
            alert('请按 F12 键打开开发者工具，然后切换到 Console 标签页');
        }
        
        function checkStatus() {
            const baseurl = localStorage.getItem("baseurl") || sessionStorage.getItem("baseurl");
            const header = sessionStorage.getItem("header");
            
            let status = [];
            
            if (!baseurl) {
                status.push('❌ API地址未配置');
            } else {
                status.push('✅ API地址: ' + baseurl);
            }
            
            if (!header) {
                status.push('❌ 认证信息未设置');
            } else {
                status.push('✅ 认证信息已设置');
            }
            
            alert('系统状态检查:\n\n' + status.join('\n'));
        }
    </script>
</body>
</html>
