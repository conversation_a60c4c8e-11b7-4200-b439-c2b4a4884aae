<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>帮助中心 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .help-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
        }

        .search-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .search-box {
            position: relative;
        }

        .search-input {
            width: 100%;
            padding: 12px 16px 12px 40px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            background: #f8f9fa;
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .help-categories {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .category-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-decoration: none;
            color: #333;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .category-item:active {
            transform: scale(0.95);
            background: #f8f9fa;
        }

        .category-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: white;
        }

        .category-icon.getting-started {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .category-icon.learning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .category-icon.account {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .category-icon.technical {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .category-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .category-desc {
            font-size: 12px;
            color: #666;
            line-height: 1.3;
        }

        .faq-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
        }

        .faq-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .faq-item {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .faq-question {
            padding: 12px 16px;
            background: #f8f9fa;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: background 0.3s ease;
        }

        .faq-question:active {
            background: #e9ecef;
        }

        .faq-question .arrow {
            transition: transform 0.3s ease;
        }

        .faq-question.active .arrow {
            transform: rotate(180deg);
        }

        .faq-answer {
            padding: 0 16px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .faq-answer.active {
            padding: 12px 16px;
            max-height: 200px;
        }

        .faq-answer-content {
            font-size: 13px;
            color: #666;
            line-height: 1.5;
        }

        .contact-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .contact-item:last-child {
            border-bottom: none;
        }

        .contact-item:active {
            background: #f8f9fa;
            margin: 0 -16px;
            padding: 12px 16px;
        }

        .contact-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }

        .contact-icon.phone {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .contact-icon.email {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .contact-icon.feedback {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .contact-info {
            flex: 1;
        }

        .contact-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .contact-desc {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">帮助中心</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="help-container">
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-box">
                <svg class="search-icon" viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                    <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3C5.91 3 3 5.91 3 9.5C3 13.09 5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5C5 7.01 7.01 5 9.5 5C11.99 5 14 7.01 14 9.5C14 11.99 11.99 14 9.5 14Z"/>
                </svg>
                <input type="text" class="search-input" placeholder="搜索帮助内容..." id="searchInput">
            </div>
        </div>

        <!-- 帮助分类 -->
        <div class="help-categories">
            <a href="#" class="category-item" onclick="showCategory('getting-started')">
                <div class="category-icon getting-started">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;">
                        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                    </svg>
                </div>
                <div class="category-title">快速入门</div>
                <div class="category-desc">新手使用指南</div>
            </a>

            <a href="#" class="category-item" onclick="showCategory('learning')">
                <div class="category-icon learning">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;">
                        <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
                    </svg>
                </div>
                <div class="category-title">学习功能</div>
                <div class="category-desc">课程学习相关</div>
            </a>

            <a href="#" class="category-item" onclick="showCategory('account')">
                <div class="category-icon account">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
                    </svg>
                </div>
                <div class="category-title">账号管理</div>
                <div class="category-desc">登录注册相关</div>
            </a>

            <a href="#" class="category-item" onclick="showCategory('technical')">
                <div class="category-icon technical">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;">
                        <path d="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.68 19.18,11.36 19.13,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.93,5.93 15.45,5.64 14.92,5.42L14.5,2.81C14.46,2.57 14.25,2.4 14,2.4H10.08C9.83,2.4 9.62,2.57 9.58,2.81L9.16,5.42C8.63,5.64 8.15,5.93 7.7,6.29L5.31,5.33C5.09,5.26 4.84,5.33 4.72,5.55L2.8,8.87C2.68,9.07 2.73,9.34 2.91,9.48L4.94,11.06C4.89,11.36 4.87,11.68 4.87,12C4.87,12.33 4.89,12.64 4.94,12.94L2.91,14.52C2.73,14.66 2.68,14.93 2.8,15.13L4.72,18.45C4.84,18.67 5.09,18.74 5.31,18.67L7.7,17.71C8.15,18.07 8.63,18.36 9.16,18.58L9.58,21.19C9.62,21.43 9.83,21.6 10.08,21.6H14C14.25,21.6 14.46,21.43 14.5,21.19L14.92,18.58C15.45,18.36 15.93,18.07 16.38,17.71L18.77,18.67C18.99,18.74 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.93 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z"/>
                    </svg>
                </div>
                <div class="category-title">技术支持</div>
                <div class="category-desc">常见问题解决</div>
            </a>
        </div>

        <!-- 常见问题 -->
        <div class="faq-section">
            <div class="section-title">常见问题</div>
            <div class="faq-list">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>如何开始学习课程？</span>
                        <svg class="arrow" viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z"/>
                        </svg>
                    </div>
                    <div class="faq-answer">
                        <div class="faq-answer-content">
                            在首页点击"学习"模块，选择您感兴趣的课程类型，如课程学习、红色书籍等，然后点击具体课程即可开始学习。
                        </div>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>忘记密码怎么办？</span>
                        <svg class="arrow" viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z"/>
                        </svg>
                    </div>
                    <div class="faq-answer">
                        <div class="faq-answer-content">
                            在登录页面点击"忘记密码"，输入您的用户名或邮箱，系统会发送重置密码的链接到您的邮箱。
                        </div>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>如何查看学习进度？</span>
                        <svg class="arrow" viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z"/>
                        </svg>
                    </div>
                    <div class="faq-answer">
                        <div class="faq-answer-content">
                            进入个人中心，点击"学习路径"可以查看您的学习历史和进度，包括已完成的课程和学习时长。
                        </div>
                    </div>
                </div>

                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFaq(this)">
                        <span>如何参与社区讨论？</span>
                        <svg class="arrow" viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M7.41 8.59L12 13.17L16.59 8.59L18 10L12 16L6 10L7.41 8.59Z"/>
                        </svg>
                    </div>
                    <div class="faq-answer">
                        <div class="faq-answer-content">
                            点击底部导航的"社区"，您可以浏览其他用户的分享，也可以点击"发布心声"分享您的学习心得和感悟。
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系我们 -->
        <div class="contact-section">
            <div class="section-title">联系我们</div>
            <div class="contact-item" onclick="contactPhone()">
                <div class="contact-icon phone">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M6.62 10.79C8.06 13.62 10.38 15.94 13.21 17.38L15.41 15.18C15.69 14.9 16.08 14.82 16.43 14.93C17.55 15.3 18.75 15.5 20 15.5C20.55 15.5 21 15.95 21 16.5V20C21 20.55 20.55 21 20 21C10.61 21 3 13.39 3 4C3 3.45 3.45 3 4 3H7.5C8.05 3 8.5 3.45 8.5 4C8.5 5.25 8.7 6.45 9.07 7.57C9.18 7.92 9.1 8.31 8.82 8.59L6.62 10.79Z"/>
                    </svg>
                </div>
                <div class="contact-info">
                    <div class="contact-title">客服热线</div>
                    <div class="contact-desc">************</div>
                </div>
            </div>

            <div class="contact-item" onclick="contactEmail()">
                <div class="contact-icon email">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M20 4H4C2.9 4 2.01 4.9 2.01 6L2 18C2 19.1 2.9 20 4 20H20C21.1 20 22 19.1 22 18V6C22 4.9 21.1 4 20 4ZM20 8L12 13L4 8V6L12 11L20 6V8Z"/>
                    </svg>
                </div>
                <div class="contact-info">
                    <div class="contact-title">邮箱支持</div>
                    <div class="contact-desc"><EMAIL></div>
                </div>
            </div>

            <div class="contact-item" onclick="submitFeedback()">
                <div class="contact-icon feedback">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                    </svg>
                </div>
                <div class="contact-info">
                    <div class="contact-title">意见反馈</div>
                    <div class="contact-desc">提交您的建议和意见</div>
                </div>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化搜索功能
            initSearch();
        });

        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                if (query) {
                    // 实现搜索功能
                    console.log('搜索:', query);
                }
            });
        }

        function toggleFaq(element) {
            const answer = element.nextElementSibling;
            const isActive = element.classList.contains('active');
            
            // 关闭所有其他FAQ
            document.querySelectorAll('.faq-question.active').forEach(q => {
                q.classList.remove('active');
                q.nextElementSibling.classList.remove('active');
            });
            
            // 切换当前FAQ
            if (!isActive) {
                element.classList.add('active');
                answer.classList.add('active');
            }
        }

        function showCategory(category) {
            MobileUtils.showToast(`${category} 分类功能开发中`);
        }

        function contactPhone() {
            if (confirm('是否拨打客服热线 ************？')) {
                window.location.href = 'tel:************';
            }
        }

        function contactEmail() {
            window.location.href = 'mailto:<EMAIL>';
        }

        function submitFeedback() {
            MobileUtils.showToast('意见反馈功能开发中');
        }
    </script>
</body>
</html>
