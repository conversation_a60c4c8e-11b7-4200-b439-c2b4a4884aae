<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>任务管理 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .task-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .create-task-btn {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px;
            width: 100%;
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .create-task-btn:active {
            transform: scale(0.98);
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: #c00714;
            color: white;
        }

        .task-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .task-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .task-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .task-item.active {
            border-left: 4px solid #28a745;
        }

        .task-item.pending {
            border-left: 4px solid #ffc107;
        }

        .task-item.expired {
            border-left: 4px solid #dc3545;
        }

        .task-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .task-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .task-desc {
            font-size: 13px;
            color: #666;
            line-height: 1.4;
        }

        .task-status {
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 11px;
            font-weight: 600;
            position: absolute;
            top: 12px;
            right: 12px;
        }

        .task-status.active {
            background: #d4edda;
            color: #155724;
        }

        .task-status.pending {
            background: #fff3cd;
            color: #856404;
        }

        .task-status.expired {
            background: #f8d7da;
            color: #721c24;
        }

        .task-meta {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            font-size: 12px;
            margin-top: 12px;
        }

        .meta-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .meta-value {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .meta-label {
            color: #666;
        }

        .task-actions {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .action-btn {
            flex: 1;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: #c00714;
            color: white;
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">任务管理</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="task-container">
        <!-- 创建任务按钮 -->
        <button class="create-task-btn" onclick="createTask()">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                <path d="M19 13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
            </svg>
            创建新任务
        </button>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="active">进行中</button>
                <button class="filter-tab" data-status="pending">待开始</button>
                <button class="filter-tab" data-status="expired">已过期</button>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="task-list" id="taskList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载任务列表...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentStatus = 'all';
        let tasksData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载任务列表
            loadTasks();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const status = this.getAttribute('data-status');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentStatus = status;
                    renderTasks();
                });
            });
        }

        function loadTasks() {
            // 获取任务列表数据 - 使用PC端相同的接口
            $.ajax({
                url: baseurl + "/learning-tasks/weblist",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 100,
                    tasksName: "",
                    sectionId: ""
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        // 转换PC端数据格式为移动端需要的格式
                        tasksData = (res.data.list || []).map(item => ({
                            id: item.id,
                            title: item.tasksName,
                            name: item.tasksName,
                            description: item.description || item.content || '学习任务',
                            content: item.content,
                            startTime: item.startTime,
                            endTime: item.endTime,
                            sectionName: item.sectionName,
                            status: getTaskStatus(item),
                            createdAt: item.createdAt,
                            participantCount: 0 // 后续异步获取
                        }));
                        
                        // 异步获取参与人数
                        loadParticipantCounts();
                        renderTasks();
                    } else {
                        // 如果没有数据，加载模拟数据
                        loadMockTasks();
                    }
                },
                error: (err) => {
                    console.error('加载任务列表失败:', err);
                    // 加载模拟数据
                    loadMockTasks();
                }
            });
        }

        function getTaskStatus(task) {
            const now = new Date();
            const startTime = new Date(task.startTime);
            const endTime = new Date(task.endTime);
            
            if (now < startTime) {
                return 'pending';
            } else if (now >= startTime && now <= endTime) {
                return 'active';
            } else {
                return 'expired';
            }
        }

        function loadParticipantCounts() {
            // 为每个任务异步获取参与人数
            tasksData.forEach((task, index) => {
                $.ajax({
                    url: baseurl + "/study/record/studenttecord",
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    data: {
                        pageNum: 1,
                        pageSize: 1,
                        taskId: task.id
                    },
                    dataType: 'json',
                    success: (res) => {
                        if (res.code == '200') {
                            tasksData[index].participantCount = res.data.total || 0;
                            // 重新渲染该任务项
                            updateTaskItem(task.id, tasksData[index]);
                        }
                    },
                    error: (err) => {
                        console.warn(`获取任务 ${task.id} 参与人数失败:`, err);
                    }
                });
            });
        }

        function updateTaskItem(taskId, taskData) {
            // 更新单个任务项的显示
            const taskElement = document.querySelector(`[onclick="viewTaskDetail('${taskId}')"]`);
            if (taskElement) {
                const participantElement = taskElement.querySelector('.meta-item:last-child .meta-value');
                if (participantElement) {
                    participantElement.textContent = taskData.participantCount || 0;
                }
            }
        }

        function loadMockTasks() {
            // 模拟任务数据
            tasksData = [
                {
                    id: '1',
                    title: '思政理论学习任务',
                    name: '思政理论学习任务',
                    description: '马克思主义基本原理概论学习',
                    startTime: new Date(Date.now() - 86400000).toISOString(),
                    endTime: new Date(Date.now() + 604800000).toISOString(),
                    sectionName: '思想政治理论',
                    status: 'active',
                    participantCount: 156,
                    createdAt: new Date(Date.now() - 172800000).toISOString()
                },
                {
                    id: '2',
                    title: '红色经典阅读',
                    name: '红色经典阅读',
                    description: '阅读红色经典书籍，撰写读后感',
                    startTime: new Date(Date.now() + 86400000).toISOString(),
                    endTime: new Date(Date.now() + 1209600000).toISOString(),
                    sectionName: '红色文化',
                    status: 'pending',
                    participantCount: 0,
                    createdAt: new Date().toISOString()
                },
                {
                    id: '3',
                    title: '党史学习教育',
                    name: '党史学习教育',
                    description: '学习中国共产党历史，完成相关测试',
                    startTime: new Date(Date.now() - 1209600000).toISOString(),
                    endTime: new Date(Date.now() - 86400000).toISOString(),
                    sectionName: '党史教育',
                    status: 'expired',
                    participantCount: 89,
                    createdAt: new Date(Date.now() - 1296000000).toISOString()
                }
            ];
            renderTasks();
        }

        function renderTasks() {
            const taskList = document.getElementById('taskList');
            
            let filteredTasks = tasksData;
            if (currentStatus !== 'all') {
                filteredTasks = tasksData.filter(task => task.status === currentStatus);
            }
            
            if (filteredTasks.length === 0) {
                taskList.innerHTML = '<div class="empty-state"><div class="empty-icon">📋</div><div>暂无相关任务</div><div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">点击上方按钮创建新任务</div></div>';
                return;
            }

            let html = '';
            filteredTasks.forEach(task => {
                const statusText = getStatusText(task.status);
                const daysLeft = getDaysLeft(task.endTime);
                
                html += `
                    <div class="task-item ${task.status}" onclick="viewTaskDetail('${task.id}')">
                        <div class="task-status ${task.status}">${statusText}</div>
                        <div class="task-header">
                            <div class="task-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                                    <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"/>
                                </svg>
                            </div>
                            <div class="task-info">
                                <div class="task-title">${task.title || task.name}</div>
                                <div class="task-desc">${task.sectionName || task.description}</div>
                            </div>
                        </div>
                        <div class="task-meta">
                            <div class="meta-item">
                                <div class="meta-value">${daysLeft}</div>
                                <div class="meta-label">剩余天数</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-value">${MobileUtils.formatTime(task.endTime, 'date')}</div>
                                <div class="meta-label">截止日期</div>
                            </div>
                            <div class="meta-item">
                                <div class="meta-value">${task.participantCount || 0}</div>
                                <div class="meta-label">参与人数</div>
                            </div>
                        </div>
                        <div class="task-actions">
                            ${getActionButtons(task)}
                        </div>
                    </div>
                `;
            });

            taskList.innerHTML = html;
        }

        function getStatusText(status) {
            const statusMap = {
                'active': '进行中',
                'pending': '待开始',
                'expired': '已过期'
            };
            return statusMap[status] || '未知';
        }

        function getDaysLeft(endTime) {
            const now = new Date();
            const end = new Date(endTime);
            const diffTime = end - now;
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            
            if (diffDays < 0) return '已过期';
            if (diffDays === 0) return '今天';
            return diffDays + '天';
        }

        function getActionButtons(task) {
            switch (task.status) {
                case 'pending':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); editTask('${task.id}')">编辑</button>
                        <button class="action-btn primary" onclick="event.stopPropagation(); startTask('${task.id}')">开始</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); deleteTask('${task.id}')">删除</button>
                    `;
                case 'active':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); viewProgress('${task.id}')">查看进度</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); endTask('${task.id}')">结束任务</button>
                    `;
                case 'expired':
                    return `
                        <button class="action-btn primary" onclick="event.stopPropagation(); viewResults('${task.id}')">查看结果</button>
                        <button class="action-btn secondary" onclick="event.stopPropagation(); exportResults('${task.id}')">导出数据</button>
                    `;
                default:
                    return '';
            }
        }

        function createTask() {
            MobileUtils.showToast('创建任务功能开发中');
        }

        function viewTaskDetail(taskId) {
            window.location.href = `task-detail.html?id=${taskId}`;
        }

        function editTask(taskId) {
            MobileUtils.showToast('编辑任务功能开发中');
        }

        function startTask(taskId) {
            if (confirm('确定要开始这个任务吗？')) {
                MobileUtils.showToast('开始任务功能开发中');
            }
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？此操作无法撤销。')) {
                MobileUtils.showToast('删除任务功能开发中');
            }
        }

        function endTask(taskId) {
            if (confirm('确定要结束这个任务吗？结束后学生将无法继续提交。')) {
                MobileUtils.showToast('结束任务功能开发中');
            }
        }

        function viewProgress(taskId) {
            window.location.href = `task-progress.html?id=${taskId}`;
        }

        function viewResults(taskId) {
            window.location.href = `task-results.html?id=${taskId}`;
        }

        function exportResults(taskId) {
            MobileUtils.showToast('导出结果功能开发中');
        }
    </script>
</body>
</html>
