<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>资源详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 资源详情页面专用样式 */
        .resource-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .resource-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
        }
        
        .resource-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            font-size: 14px;
            opacity: 0.9;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .resource-content {
            background: #f8f9fa;
            min-height: calc(100vh - 200px);
        }
        
        .resource-info {
            background: white;
            padding: 20px 16px;
            margin-bottom: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-section {
            margin-bottom: 24px;
        }
        
        .info-section:last-child {
            margin-bottom: 0;
        }
        
        .info-label {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .info-content {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
        }
        
        .resource-viewer {
            background: white;
            margin: 16px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .viewer-header {
            background: #f8f9fa;
            padding: 16px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .viewer-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        
        .viewer-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #5a6fd8;
        }
        
        .action-btn.secondary {
            background: #6c757d;
        }
        
        .action-btn.secondary:hover {
            background: #5a6268;
        }
        
        .viewer-content {
            padding: 16px;
            min-height: 400px;
        }
        
        .video-player {
            width: 100%;
            height: 250px;
            background: #000;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        
        .document-viewer {
            width: 100%;
            height: 500px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #fff;
        }
        
        .resource-progress {
            background: white;
            margin: 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .progress-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
        }
        
        .progress-text {
            font-size: 12px;
            color: #999;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .resource-stats {
            background: white;
            margin: 16px;
            padding: 16px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            text-align: center;
        }
        
        .stat-item {
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #999;
        }
        
        .resource-actions {
            position: fixed;
            bottom: 80px;
            left: 16px;
            right: 16px;
            display: flex;
            gap: 12px;
            z-index: 100;
        }
        
        .action-button {
            flex: 1;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }
        
        .action-button:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .action-button.secondary {
            background: #6c757d;
            box-shadow: 0 4px 16px rgba(108, 117, 125, 0.3);
        }
        
        .action-button.secondary:hover {
            background: #5a6268;
        }
        
        .loading-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        .retry-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 16px;
        }
        
        @media (max-width: 375px) {
            .resource-header {
                padding: 16px 12px;
            }
            
            .resource-viewer {
                margin: 12px;
            }
            
            .resource-progress,
            .resource-stats {
                margin: 12px;
            }
            
            .resource-actions {
                left: 12px;
                right: 12px;
            }
        }
    </style>
</head>
<body class="mobile-resource-detail">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="goBack()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">资源详情</h2>
            </div>
            <div class="header-actions">
                <button class="share-btn" onclick="shareResource()">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18 16.08C17.24 16.08 16.56 16.38 16.04 16.85L8.91 12.7C8.96 12.47 9 12.24 9 12S8.96 11.53 8.91 11.3L15.96 7.19C16.5 7.69 17.21 8 18 8C19.66 8 21 6.66 21 5S19.66 2 18 2S15 3.34 15 5C15 5.24 15.04 5.47 15.09 5.7L8.04 9.81C7.5 9.31 6.79 9 6 9C4.34 9 3 10.34 3 12S4.34 15 6 15C6.79 15 7.5 14.69 8.04 14.19L15.16 18.34C15.11 18.55 15.08 18.77 15.08 19C15.08 20.61 16.39 21.92 18 21.92S20.92 20.61 20.92 19S19.61 17.08 18 17.08L18 16.08Z"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 资源头部信息 -->
    <section class="resource-header" id="resourceHeader">
        <!-- 内容将通过JavaScript动态加载 -->
    </section>

    <!-- 资源内容 -->
    <main class="resource-content">
        <!-- 基本信息 -->
        <div class="resource-info" id="resourceInfo">
            <!-- 内容将通过JavaScript动态加载 -->
        </div>

        <!-- 资源查看器 -->
        <div class="resource-viewer" id="resourceViewer">
            <!-- 内容将通过JavaScript动态加载 -->
        </div>

        <!-- 学习进度 -->
        <div class="resource-progress" id="resourceProgress" style="display: none;">
            <div class="progress-header">
                <span class="progress-title">学习进度</span>
                <span class="progress-text" id="progressText">0%</span>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="resource-stats" id="resourceStats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="viewCount">0</div>
                    <div class="stat-label">浏览次数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="likeCount">0</div>
                    <div class="stat-label">点赞数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="downloadCount">0</div>
                    <div class="stat-label">下载次数</div>
                </div>
            </div>
        </div>
    </main>

    <!-- 操作按钮 -->
    <div class="resource-actions">
        <button class="action-button secondary" onclick="addToFavorites()">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z"/>
            </svg>
            收藏
        </button>
        <button class="action-button" onclick="startLearning()">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                <path d="M8 5V19L13 12L8 5Z"/>
            </svg>
            开始学习
        </button>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        // 全局变量
        let currentResourceId = null;
        let currentResourceData = null;
        let learningStartTime = null;
        let learningTimer = null;
        let totalLearningTime = 0;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 获取资源ID并加载数据
            initResourceDetail();
        });

        // 初始化资源详情
        function initResourceDetail() {
            const urlParams = new URLSearchParams(window.location.search);
            currentResourceId = urlParams.get('id');
            
            if (!currentResourceId) {
                showError('缺少资源ID参数');
                return;
            }
            
            loadResourceDetail();
        }

        // 加载资源详情
        function loadResourceDetail() {
            showLoading();
            
            $.ajax({
                url: baseurl + "/course/meta/" + currentResourceId,
                type: 'GET',
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    if (res.code == '200' && res.data) {
                        currentResourceData = res.data;
                        renderResourceDetail(res.data);
                        hideLoading();
                        
                        // 记录访问
                        recordResourceView();
                    } else {
                        showError('资源数据加载失败: ' + (res.message || '未知错误'));
                    }
                },
                error: function(err) {
                    console.error('资源详情加载失败:', err);
                    showError('网络请求失败，请检查网络连接');
                }
            });
        }

        // 渲染资源详情
        function renderResourceDetail(resourceData) {
            // 渲染头部信息
            renderResourceHeader(resourceData);
            
            // 渲染基本信息
            renderResourceInfo(resourceData);
            
            // 渲染资源查看器
            renderResourceViewer(resourceData);
            
            // 渲染统计信息
            renderResourceStats(resourceData);
            
            // 更新页面标题
            document.title = resourceData.title + ' - 思政一体化平台';
        }

        // 渲染资源头部
        function renderResourceHeader(data) {
            const headerHtml = `
                <div class="resource-title">${data.title || '无标题'}</div>
                <div class="resource-meta">
                    <div class="meta-item">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                            <path d="M12 12C14.21 12 16 10.21 16 8S14.21 4 12 4S8 5.79 8 8S9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z"/>
                        </svg>
                        ${data.author || data.principal || '未知作者'}
                    </div>
                    <div class="meta-item">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                            <path d="M9 11H7V9H9V11ZM13 11H11V9H13V11ZM17 11H15V9H17V11ZM19 3H5C3.89 3 3 3.89 3 5V19C3 20.11 3.89 21 5 21H19C20.11 21 21 20.11 21 19V5C21 3.89 20.11 3 19 3ZM19 19H5V8H19V19Z"/>
                        </svg>
                        ${formatDate(data.createTime)}
                    </div>
                    <div class="meta-item">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                            <path d="M12 4.5C7 4.5 2.73 7.61 1 12C2.73 16.39 7 19.5 12 19.5S21.27 16.39 23 12C21.27 7.61 17 4.5 12 4.5ZM12 17C9.24 17 7 14.76 7 12S9.24 7 12 7S17 9.24 17 12S14.76 17 12 17ZM12 9C10.34 9 9 10.34 9 12S10.34 15 12 15S15 13.66 15 12S13.66 9 12 9Z"/>
                        </svg>
                        ${data.view || 0} 次浏览
                    </div>
                </div>
            `;
            
            document.getElementById('resourceHeader').innerHTML = headerHtml;
        }

        // 渲染基本信息
        function renderResourceInfo(data) {
            const infoHtml = `
                <div class="info-section">
                    <div class="info-label">资源描述</div>
                    <div class="info-content">${data.introduction || data.content || data.description || '暂无描述'}</div>
                </div>
                <div class="info-section">
                    <div class="info-label">资源类型</div>
                    <div class="info-content">${getResourceTypeName(data.attachType)}</div>
                </div>
                <div class="info-section">
                    <div class="info-label">所属分类</div>
                    <div class="info-content">${data.categoryName || '未分类'}</div>
                </div>
                <div class="info-section">
                    <div class="info-label">更新时间</div>
                    <div class="info-content">${formatDate(data.updateTime || data.createTime)}</div>
                </div>
            `;
            
            document.getElementById('resourceInfo').innerHTML = infoHtml;
        }

        // 渲染资源查看器
        function renderResourceViewer(data) {
            const resourceList = data.cmsResourcesCourseMetaList || [];
            
            if (resourceList.length === 0) {
                document.getElementById('resourceViewer').innerHTML = `
                    <div class="viewer-header">
                        <div class="viewer-title">暂无可用资源</div>
                    </div>
                    <div class="viewer-content">
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
                            <p>该课程暂无可预览的资源文件</p>
                        </div>
                    </div>
                `;
                return;
            }

            const firstResource = resourceList[0];
            const resourceType = firstResource.attachType || 'document';
            
            let viewerContent = '';
            
            switch (resourceType) {
                case 'video':
                    viewerContent = `
                        <div class="video-player" onclick="playVideo('${firstResource.path}')">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; margin-bottom: 8px;">▶</div>
                                <div>点击播放视频</div>
                            </div>
                        </div>
                    `;
                    break;
                case 'pdf':
                    viewerContent = `
                        <iframe class="document-viewer" src="${baseurl}${firstResource.path}" frameborder="0"></iframe>
                    `;
                    break;
                default:
                    viewerContent = `
                        <div style="text-align: center; padding: 40px; color: #999;">
                            <div style="font-size: 48px; margin-bottom: 16px;">📄</div>
                            <p>${firstResource.name || '文档资源'}</p>
                            <button class="action-btn" onclick="downloadResource('${firstResource.path}', '${firstResource.name}')">
                                下载查看
                            </button>
                        </div>
                    `;
            }
            
            const viewerHtml = `
                <div class="viewer-header">
                    <div class="viewer-title">${firstResource.name || '资源文件'}</div>
                    <div class="viewer-actions">
                        <button class="action-btn" onclick="downloadResource('${firstResource.path}', '${firstResource.name}')">
                            下载
                        </button>
                        ${resourceList.length > 1 ? `<button class="action-btn secondary" onclick="showResourceList()">更多 (${resourceList.length})</button>` : ''}
                    </div>
                </div>
                <div class="viewer-content">
                    ${viewerContent}
                </div>
            `;
            
            document.getElementById('resourceViewer').innerHTML = viewerHtml;
        }

        // 渲染统计信息
        function renderResourceStats(data) {
            document.getElementById('viewCount').textContent = data.view || 0;
            document.getElementById('likeCount').textContent = data.likeCount || 0;
            document.getElementById('downloadCount').textContent = data.downloadCount || 0;
        }

        // 获取资源类型名称
        function getResourceTypeName(attachType) {
            const typeMap = {
                'video': '视频课程',
                'pdf': 'PDF文档',
                'doc': 'Word文档',
                'ppt': '演示文稿',
                'excel': 'Excel表格',
                'image': '图片资源',
                'audio': '音频资源'
            };
            return typeMap[attachType] || '文档资源';
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN');
        }

        // 记录资源访问
        function recordResourceView() {
            $.ajax({
                url: baseurl + "/course/click/" + currentResourceId,
                type: 'GET',
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                success: function() {
                    console.log('资源访问记录成功');
                },
                error: function() {
                    console.log('资源访问记录失败');
                }
            });
        }

        // 播放视频
        function playVideo(videoPath) {
            // 这里可以集成视频播放器
            alert('视频播放功能待实现: ' + videoPath);
        }

        // 下载资源
        function downloadResource(resourcePath, fileName) {
            const link = document.createElement('a');
            link.href = baseurl + resourcePath;
            link.download = fileName;
            link.click();
            
            showToast('开始下载资源', 'success');
        }

        // 显示资源列表
        function showResourceList() {
            // 实现资源列表弹窗
            alert('资源列表功能待实现');
        }

        // 开始学习
        function startLearning() {
            if (learningStartTime === null) {
                learningStartTime = Date.now();
                
                // 显示学习进度条
                document.getElementById('resourceProgress').style.display = 'block';
                
                // 开始计时
                learningTimer = setInterval(updateLearningProgress, 1000);
                
                // 更新按钮状态
                const btn = document.querySelector('.action-button');
                btn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                        <path d="M6 19H8V5H6V19ZM9.5 12L18 18V6L9.5 12Z"/>
                    </svg>
                    学习中...
                `;
                btn.onclick = stopLearning;
                
                showToast('开始学习计时', 'success');
            }
        }

        // 停止学习
        function stopLearning() {
            if (learningStartTime !== null) {
                const sessionTime = Math.floor((Date.now() - learningStartTime) / 1000);
                totalLearningTime += sessionTime;
                
                if (learningTimer) {
                    clearInterval(learningTimer);
                    learningTimer = null;
                }
                
                learningStartTime = null;
                
                // 保存学习记录
                saveLearningRecord(sessionTime);
                
                // 更新按钮状态
                const btn = document.querySelector('.action-button');
                btn.innerHTML = `
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; margin-right: 6px;">
                        <path d="M8 5V19L13 12L8 5Z"/>
                    </svg>
                    继续学习
                `;
                btn.onclick = startLearning;
                
                showToast('学习记录已保存', 'success');
            }
        }

        // 更新学习进度
        function updateLearningProgress() {
            if (learningStartTime) {
                const currentTime = Math.floor((Date.now() - learningStartTime) / 1000);
                const totalTime = totalLearningTime + currentTime;
                
                // 假设完成时间为30分钟
                const targetTime = 30 * 60;
                const progress = Math.min((totalTime / targetTime) * 100, 100);
                
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('progressText').textContent = Math.floor(progress) + '%';
            }
        }

        // 保存学习记录
        function saveLearningRecord(sessionTime) {
            const recordData = {
                courseId: currentResourceId,
                studyTime: sessionTime,
                studyDate: new Date().toISOString().split('T')[0],
                deviceType: 'mobile',
                timestamp: Date.now()
            };

            $.ajax({
                url: baseurl + "/study/record/add",
                type: 'POST',
                data: JSON.stringify(recordData),
                headers: {
                    "Authorization": sessionStorage.getItem("header"),
                    "Content-Type": "application/json"
                },
                dataType: 'json',
                success: function(res) {
                    if (res.code == '200') {
                        console.log('学习记录保存成功:', recordData);
                    }
                },
                error: function(err) {
                    console.error('学习记录保存失败:', err);
                }
            });
        }

        // 添加到收藏
        function addToFavorites() {
            // 实现收藏功能
            showToast('收藏功能待实现', 'info');
        }

        // 分享资源
        function shareResource() {
            if (navigator.share) {
                navigator.share({
                    title: currentResourceData.title,
                    url: window.location.href
                });
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(window.location.href).then(() => {
                    showToast('链接已复制到剪贴板', 'success');
                });
            }
        }

        // 返回上一页
        function goBack() {
            if (learningStartTime !== null) {
                stopLearning();
            }
            
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = 'course-list.html';
            }
        }

        // 显示加载状态
        function showLoading() {
            const content = `
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>正在加载资源详情...</p>
                </div>
            `;
            document.querySelector('.resource-content').innerHTML = content;
        }

        // 隐藏加载状态
        function hideLoading() {
            // 加载完成后会重新渲染内容
        }

        // 显示错误状态
        function showError(message) {
            const content = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <p>${message}</p>
                    <button class="retry-btn" onclick="loadResourceDetail()">重新加载</button>
                </div>
            `;
            document.querySelector('.resource-content').innerHTML = content;
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            const bgColor = type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3';
            
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: ${bgColor};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                opacity: 0;
                transition: all 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(-50%) translateY(-10px)';
            }, 100);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(-50%) translateY(-20px)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 页面卸载时停止学习计时
        window.addEventListener('beforeunload', function() {
            if (learningStartTime !== null) {
                stopLearning();
            }
        });
    </script>
</body>
</html> 