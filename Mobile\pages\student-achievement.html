<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学生成绩统计 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .achievement-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .stats-overview {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: #c00714;
            color: white;
        }

        .achievement-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .achievement-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .achievement-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .achievement-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 12px;
        }

        .student-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #c00714;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
        }

        .student-info {
            flex: 1;
        }

        .student-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .student-id {
            font-size: 12px;
            color: #666;
        }

        .score-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .score-badge.excellent {
            background: #d4edda;
            color: #155724;
        }

        .score-badge.good {
            background: #cce5ff;
            color: #004085;
        }

        .score-badge.average {
            background: #fff3cd;
            color: #856404;
        }

        .score-badge.poor {
            background: #f8d7da;
            color: #721c24;
        }

        .achievement-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            font-size: 12px;
        }

        .detail-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .detail-label {
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">学生成绩统计</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="achievement-container">
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalStudents">0</div>
                    <div class="stat-label">学生总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgScore">0</div>
                    <div class="stat-label">平均分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passRate">0%</div>
                    <div class="stat-label">及格率</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all">全部</button>
                <button class="filter-tab" data-filter="excellent">优秀</button>
                <button class="filter-tab" data-filter="good">良好</button>
                <button class="filter-tab" data-filter="average">一般</button>
                <button class="filter-tab" data-filter="poor">待提高</button>
            </div>
        </div>

        <!-- 成绩列表 -->
        <div class="achievement-list" id="achievementList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载学生成绩...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentFilter = 'all';
        let achievementsData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载学生成绩数据
            loadStudentAchievements();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentFilter = filter;
                    renderAchievements();
                });
            });
        }

        function loadStudentAchievements() {
            // 获取学生成绩数据 - 使用PC端相同的接口
            $.ajax({
                url: baseurl + "/paper/answered",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 1000,
                    isMark: 1  // 只获取已阅卷的成绩
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        // 处理PC端返回的数据结构
                        achievementsData = (res.data.list || []).map(item => ({
                            id: item.id,
                            studentId: item.student?.userAuth?.identifier || item.studentId,
                            studentName: item.student?.name || '未知学生',
                            score: item.score || 0,
                            keguanTotalScore: item.keguanTotalScore || 0,
                            zhuguanScore: item.zhuguanScore || 0,
                            cmsTestPaper: item.cmsTestPaper || {},
                            subjectName: item.subjectName || '未知科目',
                            createdAt: item.createdAt,
                            updatedAt: item.updatedAt,
                            isMark: item.isMark
                        }));
                        updateStatsOverview();
                        renderAchievements();
                    } else {
                        // 如果没有数据，加载模拟数据
                        loadMockStudentAchievements();
                    }
                },
                error: (err) => {
                    console.error('加载学生成绩失败:', err);
                    // 加载模拟数据
                    loadMockStudentAchievements();
                }
            });
        }

        function updateStatsOverview() {
            // 统计学生数据
            const studentMap = new Map();
            let totalScore = 0;
            let passCount = 0;

            achievementsData.forEach(item => {
                const studentId = item.studentId;
                const score = parseInt(item.score) || 0;

                if (!studentMap.has(studentId)) {
                    studentMap.set(studentId, {
                        studentId: studentId,
                        studentName: item.studentName,
                        scores: [],
                        totalScore: 0,
                        avgScore: 0
                    });
                }

                const student = studentMap.get(studentId);
                student.scores.push(score);
                student.totalScore += score;
                student.avgScore = Math.round(student.totalScore / student.scores.length);

                totalScore += score;
                if (score >= 60) passCount++;
            });

            const totalStudents = studentMap.size;
            const avgScore = achievementsData.length > 0 ? Math.round(totalScore / achievementsData.length) : 0;
            const passRate = achievementsData.length > 0 ? Math.round((passCount / achievementsData.length) * 100) : 0;

            document.getElementById('totalStudents').textContent = totalStudents;
            document.getElementById('avgScore').textContent = avgScore;
            document.getElementById('passRate').textContent = passRate + '%';
        }

        function renderAchievements() {
            const achievementList = document.getElementById('achievementList');

            let filteredData = achievementsData;
            if (currentFilter !== 'all') {
                filteredData = achievementsData.filter(item => getScoreLevel(parseInt(item.score)) === currentFilter);
            }

            if (filteredData.length === 0) {
                achievementList.innerHTML = '<div class="empty-state"><div class="empty-icon">📊</div><div>暂无相关成绩数据</div></div>';
                return;
            }

            let html = '';
            filteredData.forEach(item => {
                const score = parseInt(item.score) || 0;
                const scoreLevel = getScoreLevel(score);
                const scoreLevelText = getScoreLevelText(scoreLevel);
                const paperName = item.cmsTestPaper?.name || '未知试卷';

                html += `
                    <div class="achievement-item" onclick="viewStudentDetail('${item.studentId}')">
                        <div class="achievement-header">
                            <div class="student-avatar">${item.studentName ? item.studentName.charAt(0) : 'S'}</div>
                            <div class="student-info">
                                <div class="student-name">${item.studentName || '学生'}</div>
                                <div class="student-id">学号：${item.studentId || 'N/A'}</div>
                                <div class="paper-name" style="font-size: 12px; color: #999; margin-top: 2px;">${paperName}</div>
                            </div>
                            <div class="score-badge ${scoreLevel}">${score}分</div>
                        </div>
                        <div class="achievement-details">
                            <div class="detail-item">
                                <div class="detail-value">${item.keguanTotalScore || 0}</div>
                                <div class="detail-label">客观题分</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-value">${item.zhuguanScore || 0}</div>
                                <div class="detail-label">主观题分</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-value">${scoreLevelText}</div>
                                <div class="detail-label">等级评定</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            achievementList.innerHTML = html;
        }

        function getScoreLevel(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 60) return 'average';
            return 'poor';
        }

        function getScoreLevelText(level) {
            const levelMap = {
                'excellent': '优秀',
                'good': '良好',
                'average': '一般',
                'poor': '待提高'
            };
            return levelMap[level] || '未知';
        }

        function loadMockStudentAchievements() {
            // 模拟学生成绩数据
            achievementsData = [
                {
                    id: '1',
                    studentId: '2021001',
                    studentName: '张三',
                    score: 95,
                    keguanTotalScore: 45,
                    zhuguanScore: 50,
                    cmsTestPaper: { name: '思政理论基础测试' },
                    subjectName: '思想政治理论',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    studentId: '2021002',
                    studentName: '李四',
                    score: 87,
                    keguanTotalScore: 42,
                    zhuguanScore: 45,
                    cmsTestPaper: { name: '思政理论基础测试' },
                    subjectName: '思想政治理论',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '3',
                    studentId: '2021003',
                    studentName: '王五',
                    score: 78,
                    keguanTotalScore: 38,
                    zhuguanScore: 40,
                    cmsTestPaper: { name: '中国近现代史纲要' },
                    subjectName: '中国近现代史',
                    createdAt: new Date(Date.now() - 86400000).toISOString()
                },
                {
                    id: '4',
                    studentId: '2021004',
                    studentName: '赵六',
                    score: 92,
                    keguanTotalScore: 44,
                    zhuguanScore: 48,
                    cmsTestPaper: { name: '马克思主义基本原理' },
                    subjectName: '马克思主义理论',
                    createdAt: new Date(Date.now() - 172800000).toISOString()
                },
                {
                    id: '5',
                    studentId: '2021005',
                    studentName: '钱七',
                    score: 65,
                    keguanTotalScore: 32,
                    zhuguanScore: 33,
                    cmsTestPaper: { name: '思政理论基础测试' },
                    subjectName: '思想政治理论',
                    createdAt: new Date().toISOString()
                }
            ];
            updateStatsOverview();
            renderAchievements();
        }

        function viewStudentDetail(studentId) {
            // 跳转到学生详情页
            window.location.href = `student-detail.html?id=${studentId}`;
        }
    </script>
</body>
</html>
