<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <script src="../js/jquery.min.js"></script>
    <script src="../js/mobile-base.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #c00714;
        }
        
        .test-section h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }
        
        .btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 5px 5px 5px 0;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .result {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 6px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            margin: 15px 0;
            white-space: pre-wrap;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group select,
        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">🔧 API测试页面</div>
        
        <div class="test-section">
            <h3>📊 系统状态</h3>
            <div id="systemStatus" class="status info">正在检查系统状态...</div>
            <button class="btn btn-secondary" onclick="checkSystemStatus()">重新检查</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 基础API测试</h3>
            <button class="btn" onclick="testBasicAPI()">测试基础课程API</button>
            <button class="btn" onclick="testSectionAPI()">测试章节API</button>
            <button class="btn" onclick="testCategoryAPI()">测试分类API</button>
        </div>
        
        <div class="test-section">
            <h3>🔍 筛选API测试</h3>
            <div class="form-group">
                <label>分类ID:</label>
                <input type="text" id="categoryId" placeholder="留空表示全部">
            </div>
            <div class="form-group">
                <label>课程ID:</label>
                <input type="text" id="projectId" placeholder="留空表示全部">
            </div>
            <div class="form-group">
                <label>章节ID:</label>
                <input type="text" id="sectionId" placeholder="留空表示全部">
            </div>
            <div class="form-group">
                <label>类型ID:</label>
                <input type="text" id="typeId" placeholder="留空表示全部">
            </div>
            <div class="form-group">
                <label>排序方式:</label>
                <select id="sortOrder">
                    <option value="desc">最新优先</option>
                    <option value="asc">最早优先</option>
                </select>
            </div>
            <button class="btn" onclick="testFilterAPI()">测试筛选API</button>
            <button class="btn btn-secondary" onclick="clearResults()">清空结果</button>
        </div>
        
        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="testResults" class="result">等待测试...</div>
        </div>
    </div>
    
    <script>
        let testResults = document.getElementById('testResults');
        let systemStatus = document.getElementById('systemStatus');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            testResults.textContent += `[${timestamp}] ${message}\n`;
            testResults.scrollTop = testResults.scrollHeight;
        }
        
        function clearResults() {
            testResults.textContent = '';
        }
        
        function checkSystemStatus() {
            const baseurl = localStorage.getItem("baseurl") || sessionStorage.getItem("baseurl");
            const header = sessionStorage.getItem("header");
            
            let statusHtml = '';
            let statusClass = 'info';
            
            if (!baseurl) {
                statusHtml += '❌ API地址未配置<br>';
                statusClass = 'error';
            } else {
                statusHtml += `✅ API地址: ${baseurl}<br>`;
            }
            
            if (!header) {
                statusHtml += '❌ 认证信息未设置<br>';
                statusClass = 'error';
            } else {
                statusHtml += '✅ 认证信息已设置<br>';
            }
            
            if (statusClass === 'info') {
                statusClass = 'success';
                statusHtml += '✅ 系统状态正常，可以进行API测试';
            } else {
                statusHtml += '⚠️ 请先配置系统参数';
            }
            
            systemStatus.className = `status ${statusClass}`;
            systemStatus.innerHTML = statusHtml;
        }
        
        function testBasicAPI() {
            log('开始测试基础课程API...');
            
            const params = {
                pageSize: 10,
                pageNum: 1,
                sortField: "createTime",
                sortOrder: "desc",
                _t: Date.now()
            };
            
            log('请求参数: ' + JSON.stringify(params, null, 2));
            
            $.ajax({
                url: baseurl + "/web/course",
                type: 'GET',
                data: params,
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    log('✅ 基础API测试成功');
                    log('响应数据: ' + JSON.stringify(res, null, 2));
                    
                    if (res.code == '200' && res.data) {
                        const courseList = res.data.list || res.data;
                        log(`📊 返回课程数量: ${courseList.length}`);
                        
                        if (courseList.length > 0) {
                            log('第一个课程示例: ' + JSON.stringify(courseList[0], null, 2));
                        }
                    }
                },
                error: function(err) {
                    log('❌ 基础API测试失败');
                    log('错误信息: ' + JSON.stringify(err, null, 2));
                }
            });
        }
        
        function testSectionAPI() {
            log('开始测试章节API...');
            
            $.ajax({
                url: baseurl + "/project/section/list/tree",
                type: 'GET',
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    log('✅ 章节API测试成功');
                    log('响应数据: ' + JSON.stringify(res, null, 2));
                },
                error: function(err) {
                    log('❌ 章节API测试失败');
                    log('错误信息: ' + JSON.stringify(err, null, 2));
                }
            });
        }
        
        function testCategoryAPI() {
            log('开始测试分类API...');
            
            $.ajax({
                url: baseurl + "/category/teaching",
                type: 'GET',
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    log('✅ 分类API测试成功');
                    log('响应数据: ' + JSON.stringify(res, null, 2));
                },
                error: function(err) {
                    log('❌ 分类API测试失败');
                    log('错误信息: ' + JSON.stringify(err, null, 2));
                }
            });
        }
        
        function testFilterAPI() {
            log('开始测试筛选API...');
            
            const params = {
                pageSize: 20,
                pageNum: 1,
                sortField: "createTime",
                sortOrder: document.getElementById('sortOrder').value,
                _t: Date.now()
            };
            
            // 添加筛选参数
            const categoryId = document.getElementById('categoryId').value.trim();
            const projectId = document.getElementById('projectId').value.trim();
            const sectionId = document.getElementById('sectionId').value.trim();
            const typeId = document.getElementById('typeId').value.trim();
            
            if (categoryId) params.categoryId = categoryId;
            if (projectId) params.projectId = projectId;
            if (sectionId) params.sectionId = sectionId;
            if (typeId) params.typeId = typeId;
            
            log('筛选请求参数: ' + JSON.stringify(params, null, 2));
            
            $.ajax({
                url: baseurl + "/web/course",
                type: 'GET',
                data: params,
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    log('✅ 筛选API测试成功');
                    log('响应数据: ' + JSON.stringify(res, null, 2));
                    
                    if (res.code == '200' && res.data) {
                        const courseList = res.data.list || res.data;
                        log(`📊 筛选结果数量: ${courseList.length}`);
                        
                        if (courseList.length > 0) {
                            log('筛选结果示例: ' + JSON.stringify(courseList[0], null, 2));
                        } else {
                            log('⚠️ 筛选结果为空，可能筛选条件过于严格');
                        }
                    }
                },
                error: function(err) {
                    log('❌ 筛选API测试失败');
                    log('错误信息: ' + JSON.stringify(err, null, 2));
                }
            });
        }
        
        // 页面加载时检查系统状态
        document.addEventListener('DOMContentLoaded', function() {
            checkSystemStatus();
        });
    </script>
</body>
</html>
