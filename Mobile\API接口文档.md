# PC版智慧教学平台API接口文档

## 概述

本文档详细描述了PC版智慧教学平台的所有API接口，包括请求方式、参数说明、响应格式等。

### 基础配置

```javascript
// API基础地址配置
const baseurl = (function() {
    const isProduction = window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1');
    if (isProduction) {
        return '/api';
    } else {
        return 'http://localhost:5500/api';
    }
})();
```

### 通用请求头

```javascript
headers: {
    "Authorization": sessionStorage.getItem("header"),
    "Content-Type": "application/json"
}
```

## 1. 认证授权接口

### 1.1 CAS单点登录
- **接口**: `/student/caslogin`
- **方法**: POST
- **描述**: CAS单点登录验证
- **参数**: 
  ```json
  {
    "ticket": "CAS票据"
  }
  ```

### 1.2 获取验证码
- **接口**: `/captcha`
- **方法**: GET
- **描述**: 获取登录验证码

### 1.3 用户登录
- **接口**: `/auth/login`
- **方法**: POST
- **描述**: 用户账号密码登录

## 2. 分类管理接口

### 2.1 获取所有分类
- **接口**: `/web/category/all`
- **方法**: GET
- **描述**: 获取系统所有菜单分类

### 2.2 获取书籍分类
- **接口**: `/web/category/book`
- **方法**: GET
- **描述**: 获取红色书籍分类

### 2.3 获取教师分类
- **接口**: `/web/category/teacher`
- **方法**: GET
- **描述**: 获取教师相关分类

### 2.4 获取用户分类
- **接口**: `/web/category/user`
- **方法**: GET
- **描述**: 获取心声社区用户分类

### 2.5 获取新闻分类
- **接口**: `/web/category/news`
- **方法**: GET
- **描述**: 获取新闻分类

### 2.6 获取VR分类
- **接口**: `/web/category/vr`
- **方法**: GET
- **描述**: 获取VR相关分类

### 2.7 获取教学分类
- **接口**: `/category/teaching`
- **方法**: GET
- **描述**: 获取教学资源分类

## 3. 内容管理接口

### 3.1 获取文章列表
- **接口**: `/web/posts`
- **方法**: GET
- **描述**: 获取文章/内容列表
- **参数**: 
  ```javascript
  {
    categoryId: "分类ID",
    pageSize: "每页数量",
    pageNum: "页码",
    redBookId: "红色书籍ID",
    sort: "排序字段",
    _t: "时间戳"
  }
  ```

### 3.2 获取文章详情
- **接口**: `/web/posts/{id}`
- **方法**: GET
- **描述**: 获取指定文章详情

### 3.3 文章点击量统计
- **接口**: `/posts/click/{id}`
- **方法**: POST
- **描述**: 记录文章点击量

### 3.4 用户发布的文章
- **接口**: `/posts`
- **方法**: GET
- **描述**: 获取用户发布的文章列表
- **参数**: 
  ```javascript
  {
    pageNum: "页码",
    pageSize: "每页数量",
    creator: "创建者ID",
    categoryId: "分类ID"
  }
  ```

### 3.5 按分类和标题搜索
- **接口**: `/postsbycategoryandtitle`
- **方法**: GET
- **描述**: 按分类和标题搜索内容
- **参数**: 
  ```javascript
  {
    categoryId: "分类ID",
    title: "搜索标题"
  }
  ```

## 4. 课程管理接口

### 4.1 获取课程列表
- **接口**: `/web/course`
- **方法**: GET
- **描述**: 获取课程资源列表
- **参数**: 
  ```javascript
  {
    categoryId: "分类ID",
    pageSize: "每页数量",
    pageNum: "页码",
    projectId: "项目ID",
    sectionId: "章节ID",
    nodeId: "节点ID",
    barId: "栏目ID",
    attributesId: "属性ID",
    typeId: "类型ID",
    type: "类型",
    attachType: "附件类型",
    sortField: "排序字段",
    sortOrder: "排序方向"
  }
  ```

### 4.2 添加课程
- **接口**: `/course/add`
- **方法**: POST
- **描述**: 添加新课程

### 4.3 按标题搜索课程
- **接口**: `/courseBytitle`
- **方法**: GET
- **描述**: 按标题搜索课程
- **参数**: 
  ```javascript
  {
    title: "课程标题"
  }
  ```

## 5. 学生管理接口

### 5.1 获取学生信息
- **接口**: `/student/{id}`
- **方法**: GET
- **描述**: 获取指定学生信息

### 5.2 学生班级列表
- **接口**: `/student/ClaassStudentlist`
- **方法**: GET
- **描述**: 获取学生班级列表

### 5.3 学生密码修改
- **接口**: `/student/pwd/step/three`
- **方法**: POST
- **描述**: 学生密码修改第三步

### 5.4 学生问题提交
- **接口**: `/student/question`
- **方法**: POST
- **描述**: 学生提交问题

### 5.5 学生统计PDF
- **接口**: `/student/stat/pdf`
- **方法**: POST
- **描述**: 生成学生学习统计PDF报告
- **参数**: 
  ```javascript
  {
    taskId: "任务ID"
  }
  ```

### 5.6 微信用户验证
- **接口**: `/student/wechatuserBytiket`
- **方法**: GET
- **描述**: 通过ticket验证微信用户
- **参数**: 
  ```javascript
  {
    ticket: "微信ticket"
  }
  ```

## 6. 教师管理接口

### 6.1 教师统计PDF
- **接口**: `/teacher/stat/pdf`
- **方法**: POST
- **描述**: 生成教师统计PDF报告
- **参数**: 
  ```javascript
  {
    taskId: "任务ID",
    classId: "班级ID",
    timestamp: "时间戳"
  }
  ```

## 7. 试卷管理接口

### 7.1 获取试卷详情
- **接口**: `/paper/{paperid}`
- **方法**: GET
- **描述**: 获取指定试卷详情

### 7.2 编辑试卷
- **接口**: `/paper/edit`
- **方法**: POST
- **描述**: 编辑试卷内容

### 7.3 组合试卷
- **接口**: `/paper/combination`
- **方法**: POST
- **描述**: 组合试卷

### 7.4 复制试卷
- **接口**: `/paper/copy/{id}`
- **方法**: POST
- **描述**: 复制指定试卷

### 7.5 删除试卷
- **接口**: `/paper/delete/{id}`
- **方法**: DELETE
- **描述**: 删除指定试卷

### 7.6 获取试卷列表
- **接口**: `/paper`
- **方法**: GET
- **描述**: 获取试卷列表

### 7.7 添加试卷
- **接口**: `/paper/add`
- **方法**: POST
- **描述**: 添加新试卷

### 7.8 获取已答试卷
- **接口**: `/paper/answered`
- **方法**: GET
- **描述**: 获取已答试卷列表

### 7.9 获取答卷详情
- **接口**: `/paper/answered/{answerId}`
- **方法**: GET
- **描述**: 获取指定答卷详情

### 7.10 获取所有试卷
- **接口**: `/paper/listAll`
- **方法**: GET
- **描述**: 获取所有试卷

### 7.11 试卷作答
- **接口**: `/paper/making`
- **方法**: POST
- **描述**: 试卷作答提交

### 7.12 最近试卷
- **接口**: `/paper/recent`
- **方法**: GET
- **描述**: 获取最近试卷

### 7.13 按名称搜索试卷
- **接口**: `/paper/paperall`
- **方法**: GET
- **描述**: 按名称搜索试卷
- **参数**: 
  ```javascript
  {
    name: "试卷名称"
  }
  ```

## 8. 学习任务接口

### 8.1 删除学习任务
- **接口**: `/learning-tasks/delete/{id}`
- **方法**: DELETE
- **描述**: 删除指定学习任务

### 8.2 获取任务资源
- **接口**: `/learning-tasks-resource/getByTaskIdMap/{taskId}`
- **方法**: GET
- **描述**: 根据任务ID获取资源映射

### 8.3 获取学习任务列表
- **接口**: `/learning-tasks/weblist`
- **方法**: GET
- **描述**: 获取学习任务列表

### 8.4 最新任务
- **接口**: `/task/latest`
- **方法**: GET
- **描述**: 获取最新任务

## 9. 统计分析接口

### 9.1 分类数量统计
- **接口**: `/stat/numCategory`
- **方法**: GET
- **描述**: 获取分类数量统计

### 9.2 多分类数量统计
- **接口**: `/stat/numCategorys`
- **方法**: GET
- **描述**: 获取多个分类数量统计

### 9.3 时间趋势统计
- **接口**: `/stat/timeTrend`
- **方法**: GET
- **描述**: 获取时间趋势统计数据

## 10. 项目与学科接口

### 10.1 项目章节树
- **接口**: `/project/section/list/tree`
- **方法**: GET
- **描述**: 获取项目章节树形结构

### 10.2 获取所有学科
- **接口**: `/subject/listall`
- **方法**: GET
- **描述**: 获取所有学科列表

### 10.3 学院专业
- **接口**: `/college/major`
- **方法**: GET
- **描述**: 获取学院专业信息

## 11. 绑定关系接口

### 11.1 教师班级绑定
- **接口**: `/binding/teacher-class`
- **方法**: GET/POST
- **描述**: 教师与班级的绑定关系

## 12. 文件上传接口

### 12.1 文件上传
- **接口**: `/attachments/uploads`
- **方法**: POST
- **描述**: 文件上传接口
- **Content-Type**: multipart/form-data

## 13. 系统配置接口

### 13.1 获取类型列表
- **接口**: `/types`
- **方法**: GET
- **描述**: 获取系统类型列表

### 13.2 获取属性列表
- **接口**: `/attributes`
- **方法**: GET
- **描述**: 获取系统属性列表

### 13.3 问题管理
- **接口**: `/question`
- **方法**: GET
- **描述**: 获取问题列表

## 14. 学习记录接口

### 14.1 添加学习记录
- **接口**: `/study/record/add`
- **方法**: POST
- **描述**: 添加学习记录

## 15. 友情链接接口

### 15.1 获取友情链接
- **接口**: `/web/friendlink`
- **方法**: GET
- **描述**: 获取友情链接列表

## 响应格式说明

### 标准响应格式
```json
{
  "code": "200",
  "message": "成功",
  "data": {
    // 具体数据内容
  }
}
```

### 分页响应格式
```json
{
  "code": "200",
  "message": "成功",
  "data": {
    "list": [], // 数据列表
    "total": 100, // 总数量
    "pages": 10, // 总页数
    "pageNum": 1, // 当前页码
    "pageSize": 10 // 每页数量
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带Authorization字段
2. 时间戳参数用于防止缓存，格式为毫秒级时间戳
3. 分页参数pageNum从1开始
4. 上传文件接口需要使用multipart/form-data格式
5. 部分接口支持多种HTTP方法，具体以实际API为准

## 更新日志

- 2024年版本：包含所有核心功能接口
- 支持CAS单点登录
- 完整的课程资源管理
- 学习统计与分析功能
- 试卷系统完整支持 