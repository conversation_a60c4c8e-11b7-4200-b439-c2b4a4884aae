<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习任务 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .tasks-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .task-stats {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .filter-section {
            background: white;
            border-radius: 20px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .filter-tabs {
            display: flex;
            background: #f7fafc;
            border-radius: 12px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 10px 16px;
            text-align: center;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            color: #718096;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .tasks-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .task-item {
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
            position: relative;
        }

        .task-item:active {
            transform: translateY(1px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .task-item.urgent {
            border-left: 4px solid #f56565;
        }

        .task-item.normal {
            border-left: 4px solid #4299e1;
        }

        .task-item.completed {
            opacity: 0.7;
            border-left: 4px solid #48bb78;
        }

        .task-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;
        }

        .task-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .task-icon.urgent {
            background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
        }

        .task-icon.normal {
            background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
        }

        .task-icon.completed {
            background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        }

        .task-info {
            flex: 1;
        }

        .task-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .task-desc {
            font-size: 14px;
            color: #718096;
            line-height: 1.4;
            margin-bottom: 12px;
        }

        .task-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
        }

        .task-deadline {
            display: flex;
            align-items: center;
            gap: 4px;
            color: #a0aec0;
        }

        .task-deadline.urgent {
            color: #f56565;
        }

        .task-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
        }

        .task-status.pending {
            background: #fed7d7;
            color: #c53030;
        }

        .task-status.in-progress {
            background: #bee3f8;
            color: #2b6cb0;
        }

        .task-status.completed {
            background: #c6f6d5;
            color: #2f855a;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #a0aec0;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #718096;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">学习任务</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="tasks-container">
        <!-- 任务统计 -->
        <div class="task-stats">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalTasks">0</div>
                    <div class="stat-label">总任务</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="pendingTasks">0</div>
                    <div class="stat-label">待完成</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="completedTasks">0</div>
                    <div class="stat-label">已完成</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-status="all">全部</button>
                <button class="filter-tab" data-status="pending">待完成</button>
                <button class="filter-tab" data-status="in-progress">进行中</button>
                <button class="filter-tab" data-status="completed">已完成</button>
            </div>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-list" id="tasksList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载学习任务...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentStatus = 'all';
        let tasksData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载学习任务
            loadStudentTasks();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const status = this.getAttribute('data-status');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentStatus = status;
                    renderTasks();
                });
            });
        }

        function loadStudentTasks() {
            // 获取学生任务数据 - 使用PC端相同的接口
            $.ajax({
                url: baseurl + "/learning-tasks/weblist",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 100
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        // 转换PC端数据格式为移动端需要的格式
                        tasksData = (res.data.list || []).map(item => ({
                            id: item.id,
                            title: item.tasksName,
                            name: item.tasksName,
                            description: item.description || '学习任务',
                            content: item.content,
                            deadline: item.endTime,
                            startTime: item.startTime,
                            endTime: item.endTime,
                            sectionName: item.sectionName,
                            status: getTaskStatus(item),
                            priority: getTaskPriority(item),
                            type: 'assignment'
                        }));
                        updateTaskStats();
                        renderTasks();
                    } else {
                        document.getElementById('tasksList').innerHTML = '<div class="empty-state"><div class="empty-icon">📋</div><div>暂无学习任务</div></div>';
                    }
                },
                error: (err) => {
                    console.error('加载学习任务失败:', err);
                    document.getElementById('tasksList').innerHTML = '<div class="empty-state"><div class="empty-icon">❌</div><div>加载失败，请稍后重试</div></div>';
                }
            });
        }

        function getTaskStatus(task) {
            const now = new Date();
            const startTime = new Date(task.startTime);
            const endTime = new Date(task.endTime);

            if (now < startTime) {
                return 'pending';
            } else if (now >= startTime && now <= endTime) {
                return 'in-progress';
            } else {
                return 'completed';
            }
        }

        function getTaskPriority(task) {
            const now = new Date();
            const endTime = new Date(task.endTime);
            const diffDays = (endTime - now) / (1000 * 60 * 60 * 24);

            if (diffDays <= 1) {
                return 'urgent';
            } else if (diffDays <= 3) {
                return 'normal';
            } else {
                return 'normal';
            }
        }

        function updateTaskStats() {
            const total = tasksData.length;
            const pending = tasksData.filter(task => task.status === 'pending').length;
            const completed = tasksData.filter(task => task.status === 'completed').length;
            
            document.getElementById('totalTasks').textContent = total;
            document.getElementById('pendingTasks').textContent = pending;
            document.getElementById('completedTasks').textContent = completed;
        }

        function renderTasks() {
            const tasksList = document.getElementById('tasksList');
            
            let filteredTasks = tasksData;
            if (currentStatus !== 'all') {
                filteredTasks = tasksData.filter(task => task.status === currentStatus);
            }
            
            if (filteredTasks.length === 0) {
                tasksList.innerHTML = '<div class="empty-state"><div class="empty-icon">📋</div><div>暂无相关任务</div><div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">切换筛选条件查看其他任务</div></div>';
                return;
            }

            let html = '';
            filteredTasks.forEach(task => {
                const priority = task.priority || 'normal';
                const status = task.status || 'pending';
                const isUrgent = isTaskUrgent(task.deadline);
                
                html += `
                    <div class="task-item ${priority} ${status}" onclick="viewTaskDetail('${task.id}')">
                        <div class="task-header">
                            <div class="task-icon ${isUrgent ? 'urgent' : priority}">
                                ${getTaskIcon(task.type)}
                            </div>
                            <div class="task-info">
                                <div class="task-title">${task.title || task.name}</div>
                                <div class="task-desc">${task.sectionName || task.description || '学习任务'}</div>
                            </div>
                        </div>
                        <div class="task-meta">
                            <div class="task-deadline ${isUrgent ? 'urgent' : ''}">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                    <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17L8 12L9.41 10.59L12 13.17L14.59 10.59L16 12L13 17Z"/>
                                </svg>
                                ${MobileUtils.formatTime(task.endTime)}
                            </div>
                            <div class="task-status ${status}">
                                ${getStatusText(status)}
                            </div>
                        </div>
                    </div>
                `;
            });

            tasksList.innerHTML = html;
        }

        function getTaskIcon(type) {
            const iconMap = {
                'exam': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"/></svg>',
                'assignment': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/></svg>',
                'reading': '<svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;"><path d="M21 5C19.89 4.65 18.67 4.5 17.5 4.5C15.55 4.5 13.45 4.9 12 6C10.55 4.9 8.45 4.5 6.5 4.5C5.33 4.5 4.11 4.65 3 5V18.5C4.11 18.15 5.33 18 6.5 18C8.45 18 10.55 18.4 12 19.5C13.45 18.4 15.55 18 17.5 18C18.67 18 19.89 18.15 21 18.5V5Z"/></svg>'
            };
            return iconMap[type] || iconMap['assignment'];
        }

        function getStatusText(status) {
            const statusMap = {
                'pending': '待开始',
                'in-progress': '进行中',
                'completed': '已完成'
            };
            return statusMap[status] || '未知';
        }

        function isTaskUrgent(deadline) {
            if (!deadline) return false;
            const deadlineDate = new Date(deadline);
            const now = new Date();
            const diffDays = (deadlineDate - now) / (1000 * 60 * 60 * 24);
            return diffDays <= 3 && diffDays >= 0;
        }

        function viewTaskDetail(taskId) {
            // 跳转到任务详情页
            window.location.href = `task-detail.html?id=${taskId}`;
        }
    </script>
</body>
</html>
