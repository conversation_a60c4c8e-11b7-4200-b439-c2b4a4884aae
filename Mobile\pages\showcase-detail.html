<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>成果详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .detail-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .detail-header {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .detail-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 12px;
        }

        .detail-badge.excellent {
            background: #d4edda;
            color: #155724;
        }

        .detail-badge.good {
            background: #cce5ff;
            color: #004085;
        }

        .detail-badge.normal {
            background: #fff3cd;
            color: #856404;
        }

        .detail-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            line-height: 1.4;
            margin-bottom: 16px;
        }

        .detail-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            font-size: 13px;
            color: #666;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .author-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
            margin-bottom: 16px;
        }

        .author-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
        }

        .author-details h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0 0 4px 0;
        }

        .author-details p {
            font-size: 13px;
            color: #666;
            margin: 0;
        }

        .detail-content {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .content-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .content-text {
            font-size: 15px;
            line-height: 1.6;
            color: #333;
            white-space: pre-wrap;
        }

        .detail-stats {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            color: #c00714;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .action-btn {
            flex: 1;
            padding: 12px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }

        .action-btn.primary {
            background: #c00714;
            color: white;
        }

        .action-btn.secondary {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #dee2e6;
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">成果详情</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="detail-container" id="detailContainer">
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <p>正在加载成果详情...</p>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let showcaseId = null;
        let showcaseData = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 获取URL参数中的ID
            showcaseId = getUrlParameter('id');
            
            if (showcaseId) {
                loadShowcaseDetail(showcaseId);
            } else {
                renderEmptyState();
            }
        });

        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        function loadShowcaseDetail(id) {
            // 根据ID类型调用不同的API
            const idParts = id.split('_');
            const type = idParts[0];
            const realId = idParts[1];

            switch (type) {
                case 'post':
                    loadPostDetail(realId);
                    break;
                case 'record':
                    loadRecordDetail(realId);
                    break;
                case 'achievement':
                    loadAchievementDetail(realId);
                    break;
                default:
                    renderEmptyState();
            }
        }

        function loadPostDetail(id) {
            // 尝试多个可能的API端点
            const apiEndpoints = [
                baseurl + "/web/posts/" + id,
                baseurl + "/posts/" + id,
                baseurl + "/posts/detail/" + id
            ];

            let currentEndpoint = 0;

            function tryNextEndpoint() {
                if (currentEndpoint >= apiEndpoints.length) {
                    // 所有端点都失败，使用模拟数据
                    loadMockPostDetail(id);
                    return;
                }

                $.ajax({
                    url: apiEndpoints[currentEndpoint],
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: (res) => {
                        if (res.code == '200' && res.data) {
                            const data = res.data;
                            showcaseData = {
                                id: 'post_' + data.id,
                                title: data.title || '文章标题',
                                content: data.content || '文章内容',
                                studentName: data.creator?.name || data.creatorName || '匿名用户',
                                studentClass: data.creator?.className || data.creator?.class || '未知班级',
                                level: getWorkLevel({ views: data.views, likes: data.likes }),
                                views: data.views || 0,
                                likes: data.likes || 0,
                                createdAt: data.createdAt || new Date().toISOString(),
                                category: data.category?.name || '心声分享',
                                type: 'post'
                            };
                            renderDetail();
                        } else {
                            currentEndpoint++;
                            tryNextEndpoint();
                        }
                    },
                    error: (err) => {
                        console.warn(`API端点 ${apiEndpoints[currentEndpoint]} 失败:`, err);
                        currentEndpoint++;
                        tryNextEndpoint();
                    }
                });
            }

            tryNextEndpoint();
        }

        function loadMockPostDetail(id) {
            // 模拟文章详情数据
            showcaseData = {
                id: 'post_' + id,
                title: '学习马克思主义基本原理的心得体会',
                content: `通过深入学习马克思主义基本原理，我深刻认识到马克思主义是科学的世界观和方法论。

马克思主义不仅为我们提供了认识世界的科学方法，更为我们改造世界提供了行动指南。在学习过程中，我特别感受到以下几个方面：

1. 实践是检验真理的唯一标准
马克思主义强调实践的重要性，这让我明白理论必须与实践相结合，才能发挥其指导作用。

2. 矛盾是事物发展的根本动力
通过学习矛盾论，我学会了用对立统一的观点看待问题，这对我的学习和生活都有很大帮助。

3. 人民群众是历史的创造者
这一观点让我更加深刻地理解了为人民服务的重要意义，也坚定了我为社会主义建设贡献力量的决心。

作为新时代的大学生，我们要继续深入学习马克思主义理论，用科学的理论武装头脑，在实践中不断提高自己的思想觉悟和理论水平。`,
                studentName: '张同学',
                studentClass: '马克思主义学院2021级1班',
                level: 'excellent',
                views: 156,
                likes: 23,
                createdAt: new Date().toISOString(),
                category: '学习心得',
                type: 'post'
            };
            renderDetail();
        }

        function loadRecordDetail(id) {
            // 尝试获取学习记录详情
            $.ajax({
                url: baseurl + "/study/record/" + id,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        const data = res.data;
                        showcaseData = {
                            id: 'record_' + data.id,
                            title: data.resourcesCourse?.title || data.infoTitle || '学习记录',
                            content: `学习内容：${data.resourcesCourse?.title || data.infoTitle || '未知课程'}

学习进度：${Math.round((data.positioning / data.totalInfo) * 100)}%
学习时长：${Math.round(data.positioning / 60)}分钟
完成状态：${data.positioning >= data.totalInfo ? '已完成' : '进行中'}

学习心得：
通过本次学习，我对课程内容有了更深入的理解。课程设计合理，内容丰富，让我在学习过程中收获颇丰。特别是在理论与实践结合方面，给了我很多启发。

我会继续努力学习，不断提高自己的专业水平和综合素质。`,
                            studentName: data.student?.name || '学习者',
                            studentClass: data.student?.className || '未知班级',
                            level: getWorkLevel({ progress: (data.positioning / data.totalInfo) * 100 }),
                            views: Math.floor(Math.random() * 50) + 20,
                            likes: Math.floor(Math.random() * 20) + 5,
                            createdAt: data.createdAt || new Date().toISOString(),
                            category: data.resourcesCourse?.category?.name || '学习成果',
                            type: 'learning'
                        };
                        renderDetail();
                    } else {
                        loadMockRecordDetail(id);
                    }
                },
                error: (err) => {
                    console.warn('获取学习记录详情失败:', err);
                    loadMockRecordDetail(id);
                }
            });
        }

        function loadMockRecordDetail(id) {
            showcaseData = {
                id: 'record_' + id,
                title: '《思想道德与法治》课程学习',
                content: `学习内容：思想道德与法治

学习进度：95%
学习时长：180分钟
完成状态：已完成

学习心得：
通过学习《思想道德与法治》课程，我对新时代大学生的使命和责任有了更清晰的认识。

课程从理想信念、爱国主义、人生价值、道德修养、法治观念等多个维度，全面阐述了新时代大学生应该具备的思想品质和法律素养。

特别是在学习过程中，我深刻体会到：
1. 理想信念是精神之钙，要树立正确的世界观、人生观、价值观
2. 爱国主义是中华民族的精神基因，要做忠诚的爱国者
3. 人生价值在于奉献，要在服务人民中实现人生价值
4. 道德修养是立身之本，要明大德、守公德、严私德
5. 法治观念是现代公民的基本素养，要做尊法学法守法用法的模范

这门课程不仅丰富了我的理论知识，更重要的是提升了我的思想境界，为我今后的人生道路指明了方向。`,
                studentName: '李同学',
                studentClass: '计算机科学与技术2021级1班',
                level: 'excellent',
                views: 89,
                likes: 16,
                createdAt: new Date(Date.now() - 86400000).toISOString(),
                category: '学习成果',
                type: 'learning'
            };
            renderDetail();
        }

        function loadAchievementDetail(id) {
            // 尝试获取考试成果详情
            $.ajax({
                url: baseurl + "/paper/answered/" + id,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        const data = res.data;
                        showcaseData = {
                            id: 'achievement_' + data.id,
                            title: `${data.cmsTestPaper?.name || '考试'}优秀成绩`,
                            content: `考试名称：${data.cmsTestPaper?.name || '未知考试'}

考试成绩：${data.score}分
客观题得分：${data.keguanTotalScore}分
主观题得分：${data.zhuguanScore}分
考试时间：${MobileUtils.formatTime(data.createdAt)}

成绩分析：
本次考试取得了${data.score}分的优秀成绩，体现了扎实的理论基础和良好的应用能力。

在客观题部分，得分${data.keguanTotalScore}分，说明对基础知识掌握较为牢固。
在主观题部分，得分${data.zhuguanScore}分，体现了较强的分析问题和解决问题的能力。

通过这次考试，我进一步巩固了所学知识，也发现了自己在某些方面还需要加强学习。今后我会继续努力，争取在学习上取得更大的进步。`,
                            studentName: data.student?.name || data.studentName || '考生',
                            studentClass: data.student?.className || '未知班级',
                            level: getWorkLevel({ score: parseInt(data.score) }),
                            views: Math.floor(Math.random() * 40) + 30,
                            likes: Math.floor(Math.random() * 20) + 10,
                            createdAt: data.createdAt || new Date().toISOString(),
                            category: '考试成果',
                            type: 'exam'
                        };
                        renderDetail();
                    } else {
                        loadMockAchievementDetail(id);
                    }
                },
                error: (err) => {
                    console.warn('获取考试成果详情失败:', err);
                    loadMockAchievementDetail(id);
                }
            });
        }

        function loadMockAchievementDetail(id) {
            showcaseData = {
                id: 'achievement_' + id,
                title: '《马克思主义基本原理》期末考试优秀成绩',
                content: `考试名称：马克思主义基本原理期末考试

考试成绩：92分
客观题得分：45分
主观题得分：47分
考试时间：2024年12月20日

成绩分析：
本次《马克思主义基本原理》期末考试取得了92分的优秀成绩，在全班排名前10%。

客观题部分（45分）：
- 单选题：38/40分，准确率95%
- 多选题：7/10分，需要加强对复杂概念的理解

主观题部分（47分）：
- 简答题：22/25分，回答较为完整
- 论述题：25/25分，论证逻辑清晰，理论联系实际

优势分析：
1. 对马克思主义基本概念掌握扎实
2. 能够运用马克思主义观点分析现实问题
3. 答题逻辑清晰，表达能力较强

改进方向：
1. 加强对复杂理论问题的深入理解
2. 提高多选题的准确率
3. 继续加强理论与实践的结合

这次考试不仅检验了我的学习成果，也让我更加深刻地认识到马克思主义理论的重要性。我会继续努力学习，用马克思主义理论武装头脑，指导实践。`,
                studentName: '王同学',
                studentClass: '软件工程2021级2班',
                level: 'excellent',
                views: 134,
                likes: 28,
                createdAt: new Date(Date.now() - 172800000).toISOString(),
                category: '考试成果',
                type: 'exam'
            };
            renderDetail();
        }

        function getWorkLevel(work) {
            if (work.score !== undefined) {
                if (work.score >= 90) return 'excellent';
                if (work.score >= 80) return 'good';
                return 'normal';
            } else {
                const score = (work.views || 0) + (work.likes || 0) * 5;
                if (score >= 100) return 'excellent';
                if (score >= 50) return 'good';
                return 'normal';
            }
        }

        function renderDetail() {
            if (!showcaseData) {
                renderEmptyState();
                return;
            }

            const levelText = getLevelText(showcaseData.level);
            const typeText = getTypeText(showcaseData.type);

            const html = `
                <div class="detail-header">
                    <div class="detail-badge ${showcaseData.level}">${levelText}</div>
                    <h1 class="detail-title">${showcaseData.title}</h1>
                    <div class="detail-meta">
                        <div class="meta-item">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                            </svg>
                            ${showcaseData.category}
                        </div>
                        <div class="meta-item">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                                <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17L8 12L9.41 10.59L12 13.17L14.59 10.59L16 12L13 17Z"/>
                            </svg>
                            ${MobileUtils.formatTime(showcaseData.createdAt)}
                        </div>
                        <div class="meta-item">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 14px; height: 14px;">
                                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
                            </svg>
                            ${typeText}
                        </div>
                    </div>
                </div>

                <div class="author-info">
                    <div class="author-avatar">${showcaseData.studentName.charAt(0)}</div>
                    <div class="author-details">
                        <h3>${showcaseData.studentName}</h3>
                        <p>${showcaseData.studentClass}</p>
                    </div>
                </div>

                <div class="detail-content">
                    <div class="content-title">内容详情</div>
                    <div class="content-text">${showcaseData.content || '暂无详细内容'}</div>
                </div>

                <div class="detail-stats">
                    <div class="content-title">数据统计</div>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">${showcaseData.views || 0}</div>
                            <div class="stat-label">浏览量</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${showcaseData.likes || 0}</div>
                            <div class="stat-label">点赞数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${Math.floor(Math.random() * 20) + 5}</div>
                            <div class="stat-label">收藏数</div>
                        </div>
                    </div>
                    
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="likeShowcase()">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                                <path d="M12 21.35L10.55 20.03C5.4 15.36 2 12.28 2 8.5C2 5.42 4.42 3 7.5 3C9.24 3 10.91 3.81 12 5.09C13.09 3.81 14.76 3 16.5 3C19.58 3 22 5.42 22 8.5C22 12.28 18.6 15.36 13.45 20.04L12 21.35Z"/>
                            </svg>
                            点赞
                        </button>
                        <button class="action-btn secondary" onclick="shareShowcase()">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                                <path d="M18 16.08C17.24 16.08 16.56 16.38 16.04 16.85L8.91 12.7C8.96 12.47 9 12.24 9 12S8.96 11.53 8.91 11.3L15.96 7.19C16.5 7.69 17.21 8 18 8C19.66 8 21 6.66 21 5S19.66 2 18 2 15 3.34 15 5C15 5.24 15.04 5.47 15.09 5.7L8.04 9.81C7.5 9.31 6.79 9 6 9C4.34 9 3 10.34 3 12S4.34 15 6 15C6.79 15 7.5 14.69 8.04 14.19L15.16 18.34C15.11 18.55 15.08 18.77 15.08 19C15.08 20.61 16.39 21.92 18 21.92S20.92 20.61 20.92 19C20.92 17.39 19.61 16.08 18 16.08Z"/>
                            </svg>
                            分享
                        </button>
                        <button class="action-btn secondary" onclick="collectShowcase()">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                                <path d="M17 3H7C5.9 3 5 3.9 5 5V21L12 18L19 21V5C19 3.9 18.1 3 17 3Z"/>
                            </svg>
                            收藏
                        </button>
                    </div>
                </div>
            `;

            document.getElementById('detailContainer').innerHTML = html;
        }

        function renderEmptyState() {
            document.getElementById('detailContainer').innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📄</div>
                    <div>成果详情不存在或已被删除</div>
                    <div style="font-size: 12px; margin-top: 8px; opacity: 0.7;">请返回重新选择</div>
                </div>
            `;
        }

        function getLevelText(level) {
            const levelMap = {
                'excellent': '优秀',
                'good': '良好',
                'normal': '一般'
            };
            return levelMap[level] || '未知';
        }

        function getTypeText(type) {
            const texts = {
                'post': '心声分享',
                'learning': '学习成果',
                'exam': '考试成果'
            };
            return texts[type] || '其他成果';
        }

        function likeShowcase() {
            MobileUtils.showToast('点赞成功！', 'success');
            // 这里可以调用点赞API
        }

        function shareShowcase() {
            if (navigator.share) {
                navigator.share({
                    title: showcaseData.title,
                    text: showcaseData.content,
                    url: window.location.href
                });
            } else {
                MobileUtils.showToast('分享功能开发中');
            }
        }

        function collectShowcase() {
            MobileUtils.showToast('收藏成功！', 'success');
            // 这里可以调用收藏API
        }
    </script>
</body>
</html>
