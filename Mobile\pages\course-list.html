<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>课程学习 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    <link rel="stylesheet" href="../css/learning-styles.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 课程学习页面专用样式 */
        .course-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .course-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .course-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-filter-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }

        .search-container {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .search-box {
            position: relative;
            flex: 1;
        }

        .search-input {
            width: 100%;
            height: 44px;
            padding: 0 16px 0 44px;
            border: 1px solid #e0e0e0;
            border-radius: 22px;
            background: #f8f9fa;
            font-size: 14px;
            outline: none;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: #c00714;
            background: white;
            box-shadow: 0 0 0 3px rgba(192, 7, 20, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            color: #999;
        }

        .filter-btn {
            width: 44px;
            height: 44px;
            border: 1px solid #e0e0e0;
            border-radius: 22px;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover, .filter-btn.active {
            background: #c00714;
            border-color: #c00714;
            color: white;
        }

        .filter-btn svg {
            width: 18px;
            height: 18px;
        }
        
        .category-tabs {
            background: white;
            padding: 16px 16px 0;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .tab-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 16px;
        }
        
        .tab-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-tab {
            background: #f5f5f5;
            color: #666;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-tab.active {
            background: #667eea;
            color: white;
        }
        
        .course-content {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 200px);
        }
        
        .course-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .course-card {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }

        .course-card:active {
            transform: scale(0.98);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .course-cover {
            position: relative;
            height: 120px;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .course-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 1;
        }

        .course-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            position: relative;
            z-index: 2;
            border-radius: 4px;
            transition: transform 0.3s ease;
        }

        .course-card:active .course-cover img {
            transform: scale(0.95);
        }

        .course-type {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(192, 7, 20, 0.9);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            z-index: 3;
        }

        .course-info {
            padding: 12px;
        }

        .course-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .course-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
        }

        .course-author {
            display: block;
            margin-bottom: 4px;
        }

        .course-time {
            color: #999;
        }

        .course-score {
            font-size: 12px;
            color: #ff6b35;
            font-weight: 600;
        }
        
        .load-more-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 24px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            margin: 24px auto 0;
            display: block;
            transition: all 0.3s ease;
        }
        
        .load-more-btn:active {
            transform: scale(0.98);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            color: #ddd;
        }

        /* 筛选区域样式 */
        .filter-section {
            background: white;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .filter-header {
            padding: 12px 16px;
        }
        
        .filter-toggle {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 10px 12px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-toggle:hover {
            background: #e9ecef;
        }
        
        .filter-toggle.active .filter-arrow {
            transform: rotate(180deg);
        }
        
        .filter-arrow {
            transition: transform 0.3s ease;
        }
        
        .filter-content {
            padding: 0 16px 16px;
            border-top: 1px solid #f0f0f0;
            animation: filterSlideDown 0.3s ease;
        }
        
        @keyframes filterSlideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .filter-group {
            margin-bottom: 20px;
        }
        
        .filter-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .filter-options {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-option {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .filter-option.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .filter-option:hover:not(.active) {
            background: #e9ecef;
        }
        
        .filter-actions {
            display: flex;
            gap: 12px;
            padding-top: 16px;
            border-top: 1px solid #f0f0f0;
        }
        
        .filter-reset {
            flex: 1;
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-reset:hover {
            background: #e9ecef;
        }
        
        .filter-apply {
            flex: 2;
            background: #667eea;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-apply:hover {
            background: #5a6fd8;
        }

        /* 新的快速分类和筛选面板样式 */
        .quick-categories {
            margin-bottom: 16px;
        }

        .category-scroll {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
            padding-bottom: 4px;
        }

        .category-scroll::-webkit-scrollbar {
            display: none;
        }

        .quick-category {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 13px;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            flex-shrink: 0;
        }

        .quick-category.active {
            background: #c00714;
            color: white;
            border-color: #c00714;
        }

        .quick-category:hover:not(.active) {
            background: #e9ecef;
        }

        .filter-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin-top: 8px;
            border: 1px solid #e9ecef;
            animation: slideDown 0.3s ease;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .filter-row {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
        }

        .filter-item {
            flex: 1;
        }

        .filter-title {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }

        .filter-select {
            width: 100%;
            height: 36px;
            padding: 0 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            color: #333;
            outline: none;
            cursor: pointer;
            transition: border-color 0.3s ease;
        }

        .filter-select:focus {
            border-color: #c00714;
        }

        .filter-select:disabled {
            background-color: #f8f9fa;
            color: #999;
            cursor: not-allowed;
            border-color: #e9ecef;
        }

        .filter-actions {
            display: flex;
            gap: 8px;
        }

        .filter-reset-btn, .filter-apply-btn {
            flex: 1;
            height: 36px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-reset-btn {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e9ecef;
        }

        .filter-reset-btn:hover {
            background: #e9ecef;
        }

        .filter-apply-btn {
            background: #c00714;
            color: white;
        }

        .filter-apply-btn:hover {
            background: #a00610;
        }

        @media (min-width: 768px) {
            .course-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (max-width: 375px) {
            .course-header {
                padding: 16px 12px;
            }

            .course-content {
                padding: 12px;
            }

            .course-grid {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .course-cover {
                height: 100px;
            }

            .course-info {
                padding: 10px;
            }

            .course-title {
                font-size: 13px;
            }
        }
    </style>
</head>
<body class="mobile-course-list">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">课程学习</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 课程头部 -->
    <section class="course-header">
        <div class="course-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM18 14H6V6H18V14ZM8 15.5H16V17.5H8V15.5Z"/>
            </svg>
            课程学习
        </div>
        <div class="course-subtitle">系统化学习，全面提升思政素养</div>
    </section>

    <!-- 优化后的搜索和筛选区域 -->
    <section class="search-filter-section">
        <!-- 搜索框 -->
        <div class="search-container">
            <div class="search-box">
                <input type="text" class="search-input" placeholder="搜索课程名称、关键词..." id="searchInput">
                <svg class="search-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14H14.71L14.43 13.73C15.41 12.59 16 11.11 16 9.5C16 5.91 13.09 3 9.5 3S3 5.91 3 9.5S5.91 16 9.5 16C11.11 16 12.59 15.41 13.73 14.43L14 14.71V15.5L19 20.49L20.49 19L15.5 14ZM9.5 14C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5S14 7.01 14 9.5S11.99 14 9.5 14Z"/>
                </svg>
            </div>
            <button class="filter-btn" id="filterBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M10 18H14V16H10V18ZM3 6V8H21V6H3ZM6 13H18V11H6V13Z"/>
                </svg>
            </button>
        </div>

        <!-- 快速分类标签 -->
        <div class="quick-categories">
            <div class="category-scroll">
                <button class="quick-category active" data-category="all">全部</button>
                <button class="quick-category" data-category="video">视频课程</button>
                <button class="quick-category" data-category="document">文档资料</button>
                <button class="quick-category" data-category="presentation">演示文稿</button>
                <button class="quick-category" data-category="interactive">互动课程</button>
                <button class="quick-category" data-category="required">必修课程</button>
                <button class="quick-category" data-category="popular">热门推荐</button>
                <button class="quick-category" data-category="newest">最新课程</button>
            </div>
        </div>

        <!-- 课程和章节筛选面板 -->
        <div class="filter-panel" id="filterPanel" style="display: none;">
            <!-- 主要筛选：课程分类和章节分类 -->
            <div class="filter-row">
                <div class="filter-item">
                    <label class="filter-title">课程分类</label>
                    <select class="filter-select" id="courseSelect">
                        <option value="all">全部课程</option>
                        <!-- 动态加载课程列表 -->
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-title">章节分类</label>
                    <select class="filter-select" id="sectionSelect" disabled>
                        <option value="all">全部章节</option>
                        <!-- 根据选择的课程动态加载 -->
                    </select>
                </div>
            </div>

            <!-- 辅助筛选：排序和类型 -->
            <div class="filter-row">
                <div class="filter-item">
                    <label class="filter-title">排序方式</label>
                    <select class="filter-select" id="sortSelect">
                        <option value="createTime-desc">最新发布</option>
                        <option value="view-desc">热门推荐</option>
                        <option value="rating-desc">评分最高</option>
                        <option value="title-asc">标题排序</option>
                    </select>
                </div>
                <div class="filter-item">
                    <label class="filter-title">资源类型</label>
                    <select class="filter-select" id="typeSelect">
                        <option value="all">全部类型</option>
                        <!-- 动态加载 -->
                    </select>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="filter-actions">
                <button class="filter-reset-btn" id="filterResetBtn">重置筛选</button>
                <button class="filter-apply-btn" id="filterApplyBtn">应用筛选</button>
            </div>
        </div>
    </section>

    <!-- 课程内容 -->
    <main class="course-content">
        <div class="course-grid" id="courseGrid">
            <!-- 课程卡片将通过JavaScript动态加载 -->
        </div>
        
        <!-- 加载更多按钮 -->
        <button class="load-more-btn" id="loadMoreBtn" style="display: none;">加载更多</button>
        
        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12S6.48 22 12 22S22 17.52 22 12S17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z"/>
            </svg>
            <p>暂无课程数据</p>
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        // 真实数据存储
        let coursesData = [];
        let categoriesData = [];
        let sectionsData = [];
        let typesData = [];
        let attributesData = [];
        let allCoursesData = []; // 存储所有课程数据用于筛选面板

        // 筛选状态
        let currentCategory = 'all';
        let currentCourse = 'all'; // 新增：当前选择的课程
        let currentSection = 'all';
        let currentType = 'all';
        let currentAttribute = 'all';
        let currentSort = 'createTime-desc';
        let currentPage = 1;
        const pageSize = 10;
        let filteredCourses = [];
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载分类数据和课程列表
            loadCategoriesAndCourses();
            
            // 绑定事件
            bindEvents();
        });

        // 加载分类和课程数据
        function loadCategoriesAndCourses() {
            showLoading();
            
            // 并行加载所有必要数据
            Promise.all([
                loadCategories(),
                loadSections(),
                loadTypes(),
                loadAttributes(),
                loadAllCourses() // 加载所有课程用于筛选面板
            ]).then(() => {
                // 初始化筛选事件
                initFilterEvents();

                // 使用新的API方式加载课程数据
                loadCoursesWithFilters();
            }).catch(error => {
                console.error('加载数据失败:', error);
                showError('加载数据失败，请稍后重试');
                hideLoading();
            });
        }

        // 加载分类数据
        function loadCategories() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: baseurl + "/category/teaching",
                    type: 'GET',
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == '200' && res.data) {
                            categoriesData = res.data;
                            updateCategoryTabs();
                            resolve(res.data);
                        } else {
                            // 如果分类加载失败，使用默认分类
                            categoriesData = getDefaultCategories();
                            updateCategoryTabs();
                            resolve(categoriesData);
                        }
                    },
                    error: function(err) {
                        console.warn('分类加载失败，使用默认分类:', err);
                        categoriesData = getDefaultCategories();
                        updateCategoryTabs();
                        resolve(categoriesData);
                    }
                });
            });
        }

        // 获取默认分类
        function getDefaultCategories() {
            return [
                { id: 'theory', name: '理论课程' },
                { id: 'practice', name: '实践课程' },
                { id: 'party', name: '党史教育' },
                { id: 'moral', name: '道德修养' },
                { id: 'law', name: '法律基础' },
                { id: 'situation', name: '形势政策' },
                { id: 'culture', name: '文化传承' }
            ];
        }

        // 更新分类标签
        function updateCategoryTabs() {
            // 更新新的快速分类
            const categoryContainer = document.querySelector('.category-scroll');
            if (categoryContainer) {
                let tabsHtml = '<button class="quick-category active" data-category="all">全部</button>';

                categoriesData.forEach(category => {
                    tabsHtml += `<button class="quick-category" data-category="${category.id || category.categoryId}">${category.name}</button>`;
                });

                categoryContainer.innerHTML = tabsHtml;
            }

            // 兼容旧的分类标签（如果存在）
            const tabContainer = document.querySelector('.tab-scroll');
            if (tabContainer) {
                let tabsHtml = '<button class="category-tab active" data-category="all">全部</button>';

                categoriesData.forEach(category => {
                    tabsHtml += `<button class="category-tab" data-category="${category.id || category.categoryId}">${category.name}</button>`;
                });

                tabContainer.innerHTML = tabsHtml;
            }

            // 重新绑定分类切换事件
            bindCategoryEvents();
        }

        // 加载章节数据
        function loadSections() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: baseurl + "/project/section/list/tree",
                    type: 'GET',
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == '200' && res.data) {
                            sectionsData = flattenSectionTree(res.data);
                            updateSectionOptions();
                            resolve(res.data);
                        } else {
                            sectionsData = getDefaultSections();
                            updateSectionOptions();
                            resolve(sectionsData);
                        }
                    },
                    error: function(err) {
                        console.warn('章节加载失败，使用默认章节:', err);
                        sectionsData = getDefaultSections();
                        updateSectionOptions();
                        resolve(sectionsData);
                    }
                });
            });
        }

        // 扁平化章节树
        function flattenSectionTree(treeData) {
            const sections = [];
            function traverse(nodes) {
                if (Array.isArray(nodes)) {
                    nodes.forEach(node => {
                        sections.push({
                            id: node.id || node.sectionId,
                            name: node.name || node.sectionName,
                            level: node.level || 1
                        });
                        if (node.children && node.children.length > 0) {
                            traverse(node.children);
                        }
                    });
                }
            }
            traverse(treeData);
            return sections;
        }

        // 获取默认章节
        function getDefaultSections() {
            return [
                { id: 'introduction', name: '课程导论' },
                { id: 'basic', name: '基础理论' },
                { id: 'advanced', name: '进阶学习' },
                { id: 'practice', name: '实践应用' },
                { id: 'summary', name: '总结评价' }
            ];
        }

        // 更新章节选项
        function updateSectionOptions(courseId = null) {
            const sectionSelect = document.getElementById('sectionSelect');
            if (!sectionSelect) return;

            if (courseId && courseId !== 'all') {
                // 根据选择的课程加载对应章节
                loadCourseSections(courseId).then(courseSections => {
                    let optionsHtml = '<option value="all">全部章节</option>';

                    courseSections.forEach(section => {
                        const prefix = section.level > 1 ? '　'.repeat(section.level - 1) : '';
                        optionsHtml += `<option value="${section.id}">${prefix}${section.name}</option>`;
                    });

                    sectionSelect.innerHTML = optionsHtml;
                    sectionSelect.disabled = false;
                }).catch(error => {
                    console.warn('加载课程章节失败:', error);
                    // 使用默认章节
                    updateDefaultSections();
                });
            } else {
                // 显示所有章节或默认章节
                updateDefaultSections();
            }
        }

        // 更新默认章节选项
        function updateDefaultSections() {
            const sectionSelect = document.getElementById('sectionSelect');
            if (sectionSelect) {
                let optionsHtml = '<option value="all">全部章节</option>';

                sectionsData.forEach(section => {
                    const prefix = section.level > 1 ? '　'.repeat(section.level - 1) : '';
                    optionsHtml += `<option value="${section.id}">${prefix}${section.name}</option>`;
                });

                sectionSelect.innerHTML = optionsHtml;
                sectionSelect.disabled = currentCourse === 'all';
            }

            // 兼容旧的按钮式筛选（如果存在）
            const sectionContainer = document.getElementById('sectionOptions');
            if (sectionContainer) {
                let sectionsHtml = '<button class="filter-option active" data-section="all">全部章节</button>';

                sectionsData.forEach(section => {
                    const prefix = section.level > 1 ? '　'.repeat(section.level - 1) : '';
                    sectionsHtml += `<button class="filter-option" data-section="${section.id}">${prefix}${section.name}</button>`;
                });

                sectionContainer.innerHTML = sectionsHtml;
            }
        }

        // 根据课程ID加载对应的章节
        function loadCourseSections(courseId) {
            return new Promise((resolve, reject) => {
                // 首先从已加载的课程数据中查找对应的课程
                const selectedCourse = allCoursesData.find(course => course.id === courseId);

                if (selectedCourse && selectedCourse.children && selectedCourse.children.length > 0) {
                    // 如果找到课程且有子章节，直接使用
                    const courseSections = flattenSectionTree(selectedCourse.children);
                    resolve(courseSections);
                } else {
                    // 否则重新请求章节数据
                    $.ajax({
                        url: baseurl + "/project/section/list/tree",
                        type: 'GET',
                        headers: {
                            "Authorization": sessionStorage.getItem("header")
                        },
                        dataType: 'json',
                        success: function(res) {
                            if (res.code == '200' && res.data && Array.isArray(res.data)) {
                                // 查找指定课程的章节
                                const targetCourse = res.data.find(course => course.id === courseId);
                                if (targetCourse && targetCourse.children) {
                                    const courseSections = flattenSectionTree(targetCourse.children);
                                    resolve(courseSections);
                                } else {
                                    // 如果没有找到课程章节，返回默认章节
                                    resolve(getDefaultSections());
                                }
                            } else {
                                resolve(getDefaultSections());
                            }
                        },
                        error: function(err) {
                            console.warn('加载课程章节失败:', err);
                            resolve(getDefaultSections());
                        }
                    });
                }
            });
        }

        // 加载类型数据
        function loadTypes() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: baseurl + "/types",
                    type: 'GET',
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == '200' && res.data) {
                            typesData = res.data;
                            updateTypeOptions();
                            resolve(res.data);
                        } else {
                            typesData = getDefaultTypes();
                            updateTypeOptions();
                            resolve(typesData);
                        }
                    },
                    error: function(err) {
                        console.warn('类型加载失败，使用默认类型:', err);
                        typesData = getDefaultTypes();
                        updateTypeOptions();
                        resolve(typesData);
                    }
                });
            });
        }

        // 获取默认类型
        function getDefaultTypes() {
            return [
                { id: 'video', name: '视频课程' },
                { id: 'document', name: '文档资料' },
                { id: 'presentation', name: '演示文稿' },
                { id: 'interactive', name: '互动课程' },
                { id: 'assessment', name: '考核评价' }
            ];
        }

        // 更新类型选项
        function updateTypeOptions() {
            // 更新新的选择器
            const typeSelect = document.getElementById('typeSelect');
            if (typeSelect) {
                let optionsHtml = '<option value="all">全部类型</option>';

                typesData.forEach(type => {
                    optionsHtml += `<option value="${type.id || type.typeId}">${type.name}</option>`;
                });

                typeSelect.innerHTML = optionsHtml;
            }

            // 兼容旧的按钮式筛选（如果存在）
            const typeContainer = document.getElementById('typeOptions');
            if (typeContainer) {
                let typesHtml = '<button class="filter-option active" data-type="all">全部类型</button>';

                typesData.forEach(type => {
                    typesHtml += `<button class="filter-option" data-type="${type.id || type.typeId}">${type.name}</button>`;
                });

                typeContainer.innerHTML = typesHtml;
            }
        }

        // 加载属性数据
        function loadAttributes() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: baseurl + "/attributes",
                    type: 'GET',
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == '200' && res.data) {
                            attributesData = res.data;
                            updateAttributeOptions();
                            resolve(res.data);
                        } else {
                            attributesData = getDefaultAttributes();
                            updateAttributeOptions();
                            resolve(attributesData);
                        }
                    },
                    error: function(err) {
                        console.warn('属性加载失败，使用默认属性:', err);
                        attributesData = getDefaultAttributes();
                        updateAttributeOptions();
                        resolve(attributesData);
                    }
                });
            });
        }

        // 获取默认属性
        function getDefaultAttributes() {
            return [
                { id: 'required', name: '必修课程' },
                { id: 'elective', name: '选修课程' },
                { id: 'popular', name: '热门课程' },
                { id: 'recommended', name: '推荐课程' },
                { id: 'newest', name: '最新课程' }
            ];
        }

        // 更新属性选项
        function updateAttributeOptions() {
            // 更新新的选择器
            const attributeSelect = document.getElementById('attributeSelect');
            if (attributeSelect) {
                let optionsHtml = '<option value="all">全部属性</option>';

                attributesData.forEach(attribute => {
                    optionsHtml += `<option value="${attribute.id || attribute.attributeId}">${attribute.name}</option>`;
                });

                attributeSelect.innerHTML = optionsHtml;
            }

            // 兼容旧的按钮式筛选（如果存在）
            const attributeContainer = document.getElementById('attributeOptions');
            if (attributeContainer) {
                let attributesHtml = '<button class="filter-option active" data-attribute="all">全部属性</button>';

                attributesData.forEach(attribute => {
                    attributesHtml += `<button class="filter-option" data-attribute="${attribute.id || attribute.attributeId}">${attribute.name}</button>`;
                });

                attributeContainer.innerHTML = attributesHtml;
            }
        }

        // 加载所有课程数据（用于筛选面板）
        function loadAllCourses() {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: baseurl + "/project/section/list/tree",
                    type: 'GET',
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        if (res.code == '200' && res.data && Array.isArray(res.data)) {
                            // 从 section tree 数据中提取课程信息
                            allCoursesData = extractCoursesFromSectionTree(res.data);
                            updateCourseOptions(); // 更新课程选择器
                            resolve(allCoursesData);
                        } else {
                            // 使用默认课程数据
                            allCoursesData = getDefaultCourses();
                            updateCourseOptions();
                            resolve(allCoursesData);
                        }
                    },
                    error: function(err) {
                        console.warn('课程列表加载失败，使用默认数据:', err);
                        allCoursesData = getDefaultCourses();
                        updateCourseOptions();
                        resolve(allCoursesData);
                    }
                });
            });
        }

        // 从章节树数据中提取课程信息
        function extractCoursesFromSectionTree(sectionTreeData) {
            const courses = [];

            // 遍历章节树的根节点，每个根节点代表一个课程
            sectionTreeData.forEach((rootSection, index) => {
                if (rootSection.name) {
                    courses.push({
                        id: rootSection.id || `course_${index}`,
                        title: rootSection.name,
                        sectionId: rootSection.id,
                        category: 'course',
                        level: rootSection.level || 0,
                        children: rootSection.children || []
                    });
                }
            });

            return courses;
        }

        // 获取默认课程数据
        function getDefaultCourses() {
            return [
                {
                    id: 'course1',
                    title: '思想道德与法治',
                    category: 'course',
                    sectionId: 'course1',
                    children: [
                        { id: 'section1-1', name: '第一章 人生的青春之问', level: 1 },
                        { id: 'section1-2', name: '第二章 坚定理想信念', level: 1 },
                        { id: 'section1-3', name: '第三章 弘扬中国精神', level: 1 }
                    ]
                },
                {
                    id: 'course2',
                    title: '中国近现代史纲要',
                    category: 'course',
                    sectionId: 'course2',
                    children: [
                        { id: 'section2-1', name: '第一章 反对外国侵略的斗争', level: 1 },
                        { id: 'section2-2', name: '第二章 对国家出路的早期探索', level: 1 },
                        { id: 'section2-3', name: '第三章 辛亥革命与君主专制制度的终结', level: 1 }
                    ]
                },
                {
                    id: 'course3',
                    title: '马克思主义基本原理',
                    category: 'course',
                    sectionId: 'course3',
                    children: [
                        { id: 'section3-1', name: '第一章 马克思主义是关于无产阶级和人类解放的科学', level: 1 },
                        { id: 'section3-2', name: '第二章 世界的物质性及发展规律', level: 1 },
                        { id: 'section3-3', name: '第三章 实践与认识及其发展规律', level: 1 }
                    ]
                },
                {
                    id: 'course4',
                    title: '毛泽东思想和中国特色社会主义理论体系概论',
                    category: 'course',
                    sectionId: 'course4',
                    children: [
                        { id: 'section4-1', name: '第一章 毛泽东思想及其历史地位', level: 1 },
                        { id: 'section4-2', name: '第二章 新民主主义革命理论', level: 1 },
                        { id: 'section4-3', name: '第三章 社会主义改造理论', level: 1 }
                    ]
                },
                {
                    id: 'course5',
                    title: '形势与政策',
                    category: 'course',
                    sectionId: 'course5',
                    children: [
                        { id: 'section5-1', name: '第一章 当前国际形势', level: 1 },
                        { id: 'section5-2', name: '第二章 国内政策解读', level: 1 },
                        { id: 'section5-3', name: '第三章 社会热点分析', level: 1 }
                    ]
                }
            ];
        }

        // 更新课程选择器
        function updateCourseOptions() {
            const courseSelect = document.getElementById('courseSelect');
            if (courseSelect) {
                let optionsHtml = '<option value="all">全部课程</option>';

                // 去重并按标题排序
                const uniqueCourses = allCoursesData.reduce((acc, course) => {
                    if (!acc.find(c => c.id === course.id)) {
                        acc.push(course);
                    }
                    return acc;
                }, []).sort((a, b) => a.title.localeCompare(b.title));

                uniqueCourses.forEach(course => {
                    optionsHtml += `<option value="${course.id}">${course.title}</option>`;
                });

                courseSelect.innerHTML = optionsHtml;

                console.log('课程选择器已更新，共加载', uniqueCourses.length, '个课程');
                uniqueCourses.forEach(course => {
                    console.log('课程:', course.title, 'ID:', course.id);
                });
            }
        }

        // 加载课程数据
        function loadCourses(filterParams = {}) {
            return new Promise((resolve, reject) => {
                const [sortField, sortOrder] = currentSort.split('-');

                const params = {
                    pageSize: 100, // 加载更多数据供前端筛选
                    pageNum: 1,
                    sortField: sortField,
                    sortOrder: sortOrder,
                    _t: Date.now(),
                    ...filterParams
                };

                // 添加筛选参数
                if (currentCategory !== 'all') {
                    params.categoryId = currentCategory;
                }
                if (currentCourse !== 'all') {
                    params.courseId = currentCourse;
                }
                if (currentSection !== 'all') {
                    params.sectionId = currentSection;
                }
                if (currentType !== 'all') {
                    params.typeId = currentType;
                }
                if (currentAttribute !== 'all') {
                    params.attributesId = currentAttribute;
                }

                $.ajax({
                    url: baseurl + "/web/course",
                    type: 'GET',
                    data: params,
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: function(res) {
                        console.log('课程数据API响应:', res);
                        if (res.code == '200' && res.data) {
                            const courseList = res.data.list || res.data;
                            console.log('原始课程数据:', courseList);
                            coursesData = processCourseData(courseList);
                            console.log('处理后的课程数据:', coursesData);
                            resolve(coursesData);
                        } else {
                            console.error('课程数据加载失败:', res);
                            reject(new Error('课程数据加载失败: ' + (res.message || '未知错误')));
                        }
                    },
                    error: function(err) {
                        reject(err);
                    }
                });
            });
        }

        // 使用筛选条件加载课程数据 - 参考PC端实现
        function loadCoursesWithFilters() {
            showLoading();

            // 构建请求参数，参考PC端的getpages函数
            const [sortField, sortOrder] = currentSort.split('-');
            const params = {
                pageSize: 100, // 加载更多数据
                pageNum: 1,
                sortField: sortField || "createTime",
                sortOrder: sortOrder || "desc",
                _t: Date.now()
            };

            // 添加筛选参数 - 参考PC端的参数映射
            if (currentCategory !== 'all') {
                params.categoryId = currentCategory;
            }
            if (currentCourse !== 'all') {
                params.projectId = currentCourse; // PC端使用projectId表示课程
            }
            if (currentSection !== 'all') {
                params.sectionId = currentSection;
            }
            if (currentType !== 'all') {
                params.typeId = currentType;
            }
            if (currentAttribute !== 'all') {
                params.attributesId = currentAttribute;
            }

            console.log('发送筛选请求，参数:', params);

            $.ajax({
                url: baseurl + "/web/course", // 使用PC端相同的API
                type: 'GET',
                data: params,
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    console.log('筛选API响应:', res);
                    hideLoading();

                    if (res.code == '200' && res.data) {
                        const courseList = res.data.list || res.data;
                        console.log('筛选结果课程数量:', courseList.length);

                        // 处理课程数据
                        coursesData = processCourseData(courseList);

                        // 由于API已经进行了筛选，直接将所有数据设置为筛选结果
                        filteredCourses = coursesData;

                        console.log('API筛选完成，设置filteredCourses:', filteredCourses.length);

                        // 重新初始化课程列表
                        currentPage = 1;
                        renderCourses();

                        // 显示结果提示
                        const resultCount = coursesData.length;
                        showToast(`筛选完成，找到 ${resultCount} 个课程`, 'success');
                    } else {
                        console.error('筛选API返回错误:', res);
                        showError('筛选失败: ' + (res.message || '未知错误'));
                    }
                },
                error: function(err) {
                    console.error('筛选请求失败:', err);
                    hideLoading();
                    showError('筛选请求失败，请检查网络连接');
                }
            });
        }

        // 重新加载课程数据（应用筛选）- 保留兼容性
        function reloadCoursesWithFilter() {
            loadCoursesWithFilters();
        }

        // 处理课程数据
        function processCourseData(courseList) {
            return courseList.map(course => {
                // 处理封面图片 - 与首页保持一致的字段处理
                let coverUrl = '../img/course_default.jpg';
                if (course.coverPath && course.coverPath.length > 0) {
                    coverUrl = baseurl + course.coverPath[0];
                } else if (course.covertPath) {
                    // 兼容旧的拼写错误字段
                    coverUrl = baseurl + course.covertPath;
                }

                // 处理评分
                let rating = 4.5;
                if (course.listCourseEvaluate && course.listCourseEvaluate.length > 3) {
                    rating = parseFloat(course.listCourseEvaluate[3].zh || 4.5);
                } else if (course.score) {
                    rating = parseFloat(course.score);
                }

                // 处理课程信息 - 与首页保持一致
                return {
                    id: course.metaId || course.id || course.courseId,
                    title: course.title || course.titleName || course.name || '无标题',
                    description: course.introduction || course.content || course.summary || course.description || course.remark || '暂无课程介绍',
                    category: course.categoryId || course.attachType || course.type || 'general',
                    instructor: course.author || course.principal || course.teacher || '未知',
                    duration: calculateDuration(course),
                    rating: rating,
                    students: course.view || course.studentCount || course.clickCount || 0,
                    cover: coverUrl,
                    badge: getBadgeText(course),
                    createTime: course.createTime || course.createdAt || course.publishedTime,
                    attachType: course.attachType || '课程学习'
                };
            });
        }

        // 计算课程时长
        function calculateDuration(course) {
            if (course.duration) {
                return course.duration;
            }
            
            // 根据资源数量估算时长
            const resourceCount = course.cmsResourcesCourseMetaList ? course.cmsResourcesCourseMetaList.length : 1;
            const estimatedHours = resourceCount * 2; // 每个资源估算2小时
            return `${estimatedHours}课时`;
        }

        // 获取徽章文本
        function getBadgeText(course) {
            if (course.isRequired) {
                return '必修课';
            } else if (course.isPopular || (course.view && course.view > 1000)) {
                return '热门';
            } else if (course.isNew || isNewCourse(course.createTime)) {
                return '新课程';
            } else {
                return '选修课';
            }
        }

        // 判断是否为新课程
        function isNewCourse(createTime) {
            if (!createTime) return false;
            const courseDate = new Date(createTime);
            const now = new Date();
            const diffDays = (now - courseDate) / (1000 * 60 * 60 * 24);
            return diffDays <= 30; // 30天内的课程算新课程
        }

        // 显示加载状态
        function showLoading() {
            const courseGrid = document.getElementById('courseGrid');
            courseGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #999;">
                    <div style="width: 40px; height: 40px; border: 3px solid #f3f3f3; border-top: 3px solid #667eea; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 16px;"></div>
                    <p>正在加载课程数据...</p>
                </div>
            `;
        }

        // 隐藏加载状态
        function hideLoading() {
            // 加载完成后会重新渲染内容
        }

        // 显示错误信息
        function showError(message) {
            const courseGrid = document.getElementById('courseGrid');
            courseGrid.innerHTML = `
                <div style="grid-column: 1 / -1; text-align: center; padding: 60px 20px; color: #999;">
                    <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                    <p>${message}</p>
                    <button onclick="loadCategoriesAndCourses()" style="margin-top: 16px; padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">重新加载</button>
                </div>
            `;
        }

        function initCourseList() {
            filterCourses();
            renderCourses();
        }

        function filterCourses() {
            const searchKeyword = document.getElementById('searchInput').value.toLowerCase();

            console.log('开始筛选课程，原始数据数量:', coursesData.length);
            console.log('当前筛选条件:', {
                currentCategory,
                currentCourse,
                currentSection,
                currentType,
                currentAttribute,
                currentSort,
                searchKeyword
            });

            filteredCourses = coursesData.filter(course => {
                // 分类筛选
                const matchCategory = currentCategory === 'all' || course.category === currentCategory;

                // 课程筛选 - 根据选择的具体课程筛选
                const matchCourse = currentCourse === 'all' ||
                    course.id === currentCourse ||
                    course.courseId === currentCourse ||
                    course.metaId === currentCourse;

                // 搜索关键词筛选
                const matchSearch = !searchKeyword ||
                    course.title.toLowerCase().includes(searchKeyword) ||
                    course.description.toLowerCase().includes(searchKeyword) ||
                    course.instructor.toLowerCase().includes(searchKeyword);

                // 章节筛选 - 根据课程的章节ID或章节名称匹配
                const matchSection = currentSection === 'all' ||
                    course.sectionId === currentSection ||
                    course.section === currentSection ||
                    (course.sectionName && course.sectionName.includes(getSectionName(currentSection)));

                // 类型筛选 - 根据课程的类型ID或附件类型匹配
                const matchType = currentType === 'all' ||
                    course.typeId === currentType ||
                    course.attachType === currentType ||
                    course.resourceType === currentType;

                // 属性筛选 - 根据课程的属性匹配
                const matchAttribute = currentAttribute === 'all' ||
                    course.attributeId === currentAttribute ||
                    course.courseAttribute === currentAttribute ||
                    matchCourseAttribute(course, currentAttribute);

                const result = matchCategory && matchCourse && matchSearch && matchSection && matchType && matchAttribute;

                // 调试信息：显示第一个课程的匹配情况
                if (course === coursesData[0]) {
                    console.log('第一个课程的匹配情况:', {
                        title: course.title,
                        matchCategory,
                        matchCourse,
                        matchSearch,
                        matchSection,
                        matchType,
                        matchAttribute,
                        result
                    });
                }

                return result;
            });

            console.log('筛选后的课程数量:', filteredCourses.length);

            // 应用排序
            applySorting();
        }

        // 获取章节名称
        function getSectionName(sectionId) {
            const section = sectionsData.find(s => s.id === sectionId);
            return section ? section.name : '';
        }

        // 匹配课程属性
        function matchCourseAttribute(course, attributeId) {
            switch (attributeId) {
                case 'required':
                    return course.isRequired || course.required || course.badge === '必修课';
                case 'elective':
                    return !course.isRequired && !course.required && course.badge !== '必修课';
                case 'popular':
                    return course.isPopular || course.popular || course.badge === '热门' || (course.students && course.students > 1000);
                case 'recommended':
                    return course.isRecommended || course.recommended || course.rating > 4.0;
                case 'newest':
                    return course.isNew || course.badge === '新课程' || isNewCourse(course.createTime);
                default:
                    return true;
            }
        }

        // 应用排序
        function applySorting() {
            const [sortField, sortOrder] = currentSort.split('-');

            filteredCourses.sort((a, b) => {
                let valueA, valueB;

                switch (sortField) {
                    case 'createTime':
                        valueA = new Date(a.createTime || 0);
                        valueB = new Date(b.createTime || 0);
                        break;
                    case 'view':
                        valueA = a.students || 0;
                        valueB = b.students || 0;
                        break;
                    case 'rating':
                        valueA = parseFloat(a.rating || 0);
                        valueB = parseFloat(b.rating || 0);
                        break;
                    case 'title':
                        valueA = a.title.toLowerCase();
                        valueB = b.title.toLowerCase();
                        break;
                    default:
                        return 0;
                }

                if (sortOrder === 'desc') {
                    return valueB > valueA ? 1 : valueB < valueA ? -1 : 0;
                } else {
                    return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;
                }
            });
        }

        function renderCourses() {
            console.log('开始渲染课程，筛选后数量:', filteredCourses.length);

            const courseGrid = document.getElementById('courseGrid');
            const emptyState = document.getElementById('emptyState');
            const loadMoreBtn = document.getElementById('loadMoreBtn');

            if (!courseGrid) {
                console.error('找不到courseGrid元素');
                return;
            }

            if (filteredCourses.length === 0) {
                console.log('没有课程数据，显示空状态');
                courseGrid.innerHTML = '';
                if (emptyState) {
                    emptyState.style.display = 'block';
                }
                if (loadMoreBtn) {
                    loadMoreBtn.style.display = 'none';
                }
                return;
            }

            console.log('有课程数据，开始渲染');
            if (emptyState) {
                emptyState.style.display = 'none';
            }

            const coursesToShow = filteredCourses.slice(0, currentPage * pageSize);

            courseGrid.innerHTML = coursesToShow.map(course => {
                // 处理封面图片
                let imageUrl = '../img/course_default.jpg';
                if (course.cover && course.cover !== '../img/course_default.jpg') {
                    imageUrl = course.cover;
                }

                // 格式化时间 - 与首页保持一致
                const formatTime = (timeStr) => {
                    if (!timeStr) return '未知时间';
                    const date = new Date(timeStr);
                    const now = new Date();
                    const diffTime = now - date;
                    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

                    if (diffDays === 0) {
                        return '今天';
                    } else if (diffDays === 1) {
                        return '昨天';
                    } else if (diffDays < 7) {
                        return `${diffDays}天前`;
                    } else {
                        return date.toLocaleDateString('zh-CN', {
                            month: '2-digit',
                            day: '2-digit'
                        });
                    }
                };

                // 生成额外信息
                let extraInfo = '';
                const score = course.rating ? parseFloat(course.rating).toFixed(1) : '4.5';
                const viewCount = course.students || 0;
                extraInfo = `<div class="course-score">⭐ ${score} • 👁 ${viewCount}次观看</div>`;

                return `
                    <a href="course-detail.html?id=${course.id}" class="course-card" data-id="${course.id}" data-type="course" onclick="recordItemClick('${course.id}', 'course')">
                        <div class="course-cover">
                            <img src="${imageUrl}" alt="${course.title}" onerror="this.src='../img/course_default.jpg'">
                            <div class="course-type">${course.attachType || '课程学习'}</div>
                        </div>
                        <div class="course-info">
                            <div class="course-title">${course.title}</div>
                            <div class="course-meta">
                                <span class="course-author">作者：${course.instructor}</span>
                                <span class="course-time">${formatTime(course.createTime)}</span>
                            </div>
                            ${extraInfo}
                        </div>
                    </a>
                `;
            }).join('');

            // 绑定学习事件 - 与首页保持一致
            bindLearningEvents(courseGrid);

            // 显示/隐藏加载更多按钮
            if (coursesToShow.length < filteredCourses.length) {
                loadMoreBtn.style.display = 'block';
            } else {
                loadMoreBtn.style.display = 'none';
            }
        }

        function bindEvents() {
            bindCategoryEvents();
            bindSearchEvents();
            bindLoadMoreEvents();
        }

        // 初始化筛选事件
        function initFilterEvents() {
            // 新的筛选按钮
            const filterBtn = document.getElementById('filterBtn');
            const filterPanel = document.getElementById('filterPanel');

            if (filterBtn && filterPanel) {
                filterBtn.addEventListener('click', function() {
                    const isVisible = filterPanel.style.display !== 'none';
                    if (isVisible) {
                        filterPanel.style.display = 'none';
                        filterBtn.classList.remove('active');
                    } else {
                        filterPanel.style.display = 'block';
                        filterBtn.classList.add('active');
                    }
                });
            }

            // 快速分类事件
            bindQuickCategoryEvents();

            // 筛选操作按钮
            const filterResetBtn = document.getElementById('filterResetBtn');
            const filterApplyBtn = document.getElementById('filterApplyBtn');

            if (filterResetBtn) {
                filterResetBtn.addEventListener('click', resetFilters);
            }
            if (filterApplyBtn) {
                filterApplyBtn.addEventListener('click', applyFilters);
            }

            // 所有选择器事件绑定
            const courseSelect = document.getElementById('courseSelect');
            const sectionSelect = document.getElementById('sectionSelect');
            const sortSelect = document.getElementById('sortSelect');
            const typeSelect = document.getElementById('typeSelect');

            // 课程选择器事件 - 选择课程后加载对应章节并立即应用筛选
            if (courseSelect) {
                courseSelect.addEventListener('change', function() {
                    const selectedCourseId = this.value;
                    currentCourse = selectedCourseId;

                    // 重置章节选择
                    currentSection = 'all';

                    // 根据选择的课程更新章节选项
                    updateSectionOptions(selectedCourseId);

                    // 立即应用筛选，使用API重新加载数据
                    loadCoursesWithFilters();

                    // 显示加载提示
                    if (selectedCourseId !== 'all') {
                        const courseName = this.options[this.selectedIndex].text;
                        showToast(`正在加载课程：${courseName}...`, 'info');
                    } else {
                        showToast('正在加载全部课程...', 'info');
                    }
                });
            }

            if (sectionSelect) {
                sectionSelect.addEventListener('change', function() {
                    currentSection = this.value;

                    // 立即应用筛选，使用API重新加载数据
                    loadCoursesWithFilters();

                    // 显示章节选择提示
                    if (this.value !== 'all') {
                        const sectionName = this.options[this.selectedIndex].text;
                        showToast(`正在加载章节：${sectionName.trim()}...`, 'info');
                    } else {
                        showToast('正在加载全部章节...', 'info');
                    }
                });
            }

            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    currentSort = this.value;

                    // 立即应用排序，使用API重新加载数据
                    loadCoursesWithFilters();

                    const sortText = this.options[this.selectedIndex].text;
                    showToast(`正在应用排序：${sortText}...`, 'info');
                });
            }

            if (typeSelect) {
                typeSelect.addEventListener('change', function() {
                    currentType = this.value;

                    // 立即应用类型筛选，使用API重新加载数据
                    loadCoursesWithFilters();

                    const typeText = this.options[this.selectedIndex].text;
                    showToast(`正在筛选类型：${typeText}...`, 'info');
                });
            }
        }

        // 绑定筛选选项事件
        function bindFilterOptionEvents() {
            // 章节筛选
            document.getElementById('sectionOptions').addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-option')) {
                    setActiveFilterOption(this, e.target);
                    currentSection = e.target.dataset.section;
                }
            });

            // 类型筛选
            document.getElementById('typeOptions').addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-option')) {
                    setActiveFilterOption(this, e.target);
                    currentType = e.target.dataset.type;
                }
            });

            // 属性筛选
            document.getElementById('attributeOptions').addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-option')) {
                    setActiveFilterOption(this, e.target);
                    currentAttribute = e.target.dataset.attribute;
                }
            });

            // 排序筛选
            document.getElementById('sortOptions').addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-option')) {
                    setActiveFilterOption(this, e.target);
                    currentSort = e.target.dataset.sort;
                }
            });
        }

        // 设置激活状态
        function setActiveFilterOption(container, activeElement) {
            container.querySelectorAll('.filter-option').forEach(option => {
                option.classList.remove('active');
            });
            activeElement.classList.add('active');
        }

        // 重置筛选
        function resetFilters() {
            currentCategory = 'all';
            currentCourse = 'all';
            currentSection = 'all';
            currentType = 'all';
            currentAttribute = 'all';
            currentSort = 'createTime-desc';
            currentPage = 1;

            // 重置快速分类
            document.querySelectorAll('.quick-category').forEach(tab => {
                tab.classList.remove('active');
            });
            const allCategoryBtn = document.querySelector('.quick-category[data-category="all"]');
            if (allCategoryBtn) {
                allCategoryBtn.classList.add('active');
            }

            // 重置所有选择器
            const courseSelect = document.getElementById('courseSelect');
            const sectionSelect = document.getElementById('sectionSelect');
            const sortSelect = document.getElementById('sortSelect');
            const typeSelect = document.getElementById('typeSelect');

            if (courseSelect) {
                courseSelect.value = 'all';
            }
            if (sectionSelect) {
                sectionSelect.value = 'all';
                sectionSelect.disabled = true; // 禁用章节选择器
                // 重置为默认章节选项
                updateDefaultSections();
            }
            if (sortSelect) {
                sortSelect.value = 'createTime-desc';
            }
            if (typeSelect) {
                typeSelect.value = 'all';
            }

            // 重置搜索框
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.value = '';
            }

            // 使用API重新加载课程
            loadCoursesWithFilters();

            showToast('正在重置筛选条件...', 'info');
        }

        // 应用筛选 - 使用PC端相同的API调用方式
        function applyFilters() {
            // 获取所有选择器的当前值
            const courseSelect = document.getElementById('courseSelect');
            const sectionSelect = document.getElementById('sectionSelect');
            const sortSelect = document.getElementById('sortSelect');
            const typeSelect = document.getElementById('typeSelect');

            if (courseSelect) {
                currentCourse = courseSelect.value;
            }
            if (sectionSelect) {
                currentSection = sectionSelect.value;
            }
            if (sortSelect) {
                currentSort = sortSelect.value;
            }
            if (typeSelect) {
                currentType = typeSelect.value;
            }

            // 重置页码
            currentPage = 1;

            // 使用PC端相同的API调用方式重新加载数据
            loadCoursesWithFilters();

            // 关闭筛选面板
            const filterPanel = document.getElementById('filterPanel');
            const filterBtn = document.getElementById('filterBtn');

            if (filterPanel) {
                filterPanel.style.display = 'none';
            }
            if (filterBtn) {
                filterBtn.classList.remove('active');
            }

            // 显示筛选结果提示
            const activeFilters = [];
            if (currentCourse !== 'all') {
                const courseText = courseSelect.options[courseSelect.selectedIndex].text;
                activeFilters.push(`课程: ${courseText}`);
            }
            if (currentSection !== 'all') {
                const sectionText = sectionSelect.options[sectionSelect.selectedIndex].text;
                activeFilters.push(`章节: ${sectionText.trim()}`);
            }
            if (currentSort !== 'createTime-desc') {
                const sortText = sortSelect.options[sortSelect.selectedIndex].text;
                activeFilters.push(`排序: ${sortText}`);
            }
            if (currentType !== 'all') {
                const typeText = typeSelect.options[typeSelect.selectedIndex].text;
                activeFilters.push(`类型: ${typeText}`);
            }

            if (activeFilters.length > 0) {
                showToast(`正在应用筛选: ${activeFilters.join(', ')}...`, 'info');
            } else {
                showToast('正在加载课程...', 'info');
            }

            // 添加调试信息
            console.log('应用筛选条件:', {
                currentCourse,
                currentSection,
                currentSort,
                currentType
            });
        }

        // 绑定快速分类事件 - 使用API调用
        function bindQuickCategoryEvents() {
            document.querySelectorAll('.quick-category').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.quick-category').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    const category = this.dataset.category;

                    // 重置所有筛选条件
                    currentCategory = 'all';
                    currentCourse = 'all';
                    currentSection = 'all';
                    currentType = 'all';
                    currentAttribute = 'all';

                    // 根据快速分类设置相应的筛选条件
                    switch (category) {
                        case 'all':
                            // 全部，保持默认
                            break;
                        case 'video':
                        case 'document':
                        case 'presentation':
                        case 'interactive':
                            currentType = category;
                            break;
                        case 'required':
                        case 'popular':
                        case 'newest':
                            currentAttribute = category;
                            break;
                        default:
                            currentCategory = category;
                    }

                    currentPage = 1;

                    // 使用API重新加载数据而不是前端筛选
                    loadCoursesWithFilters();

                    // 显示切换提示
                    const categoryName = this.textContent;
                    showToast(`正在加载：${categoryName}...`, 'info');
                });
            });
        }

        // 绑定分类切换事件（保留兼容性）
        function bindCategoryEvents() {
            // 绑定新的快速分类
            bindQuickCategoryEvents();

            // 兼容旧的分类标签
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    currentCategory = this.dataset.category;
                    currentPage = 1;

                    // 使用API重新加载数据
                    loadCoursesWithFilters();

                    const categoryName = this.textContent;
                    showToast(`正在加载：${categoryName}...`, 'info');
                });
            });
        }

        // 绑定搜索事件
        function bindSearchEvents() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;
            
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const keyword = this.value.trim();
                    if (keyword.length >= 2) {
                        // 如果输入长度大于等于2，使用API搜索
                        searchCoursesByAPI(keyword);
                    } else if (keyword.length === 0) {
                        // 如果清空搜索，重新加载所有课程
                        currentPage = 1;
                        loadCoursesWithFilters();
                    }
                }, 500);
            });

            // 搜索框回车事件
            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    const keyword = this.value.trim();
                    if (keyword) {
                        searchCoursesByAPI(keyword);
                    }
                }
            });
        }

        // 使用API搜索课程
        function searchCoursesByAPI(keyword) {
            if (isLoading) return;
            
            isLoading = true;
            showLoading();
            
            $.ajax({
                url: baseurl + "/courseBytitle",
                type: 'GET',
                data: {
                    title: keyword,
                    _t: Date.now()
                },
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function(res) {
                    isLoading = false;
                    if (res.code == '200' && res.data) {
                        const searchResults = processCourseData(res.data);
                        
                        // 临时替换课程数据用于显示搜索结果
                        const originalData = coursesData;
                        coursesData = searchResults;

                        // 搜索结果直接设置为筛选结果，不需要再次筛选
                        filteredCourses = searchResults;

                        currentCategory = 'all';
                        currentPage = 1;

                        renderCourses();

                        // 显示搜索结果提示
                        if (searchResults.length > 0) {
                            showToast(`找到 ${searchResults.length} 个相关课程`, 'success');
                        } else {
                            showToast('未找到相关课程', 'info');
                        }

                        // 保存原始数据，便于后续恢复
                        coursesData._originalData = originalData;
                    } else {
                        // API搜索失败，降级为本地搜索
                        localSearch(keyword);
                    }
                },
                error: function(err) {
                    isLoading = false;
                    console.warn('API搜索失败，使用本地搜索:', err);
                    localSearch(keyword);
                }
            });
        }

        // 本地搜索（当API搜索失败时的降级方案）
        function localSearch(keyword) {
            // 恢复原始数据（如果有的话）
            if (coursesData._originalData) {
                coursesData = coursesData._originalData;
                delete coursesData._originalData;
            }

            // 本地搜索筛选
            filteredCourses = coursesData.filter(course => {
                return course.title.toLowerCase().includes(keyword.toLowerCase()) ||
                       course.description.toLowerCase().includes(keyword.toLowerCase()) ||
                       course.instructor.toLowerCase().includes(keyword.toLowerCase());
            });

            currentPage = 1;
            renderCourses();

            if (filteredCourses.length > 0) {
                showToast(`找到 ${filteredCourses.length} 个相关课程`, 'success');
            } else {
                showToast('未找到相关课程', 'info');
            }
        }

        // 绑定加载更多事件
        function bindLoadMoreEvents() {
            document.getElementById('loadMoreBtn').addEventListener('click', function() {
                currentPage++;
                renderCourses();
            });
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 如果有coco-message库，使用它
            if (window.cocoMessage) {
                cocoMessage[type](message);
                return;
            }

            // 创建自定义toast
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                opacity: 0;
                transition: opacity 0.3s ease;
                max-width: 300px;
                text-align: center;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 调试函数：检查数据状态
        function debugDataStatus() {
            console.log('=== 数据状态检查 ===');
            console.log('coursesData 长度:', coursesData.length);
            console.log('allCoursesData 长度:', allCoursesData.length);
            console.log('filteredCourses 长度:', filteredCourses.length);
            console.log('当前筛选条件:', {
                currentCategory,
                currentCourse,
                currentSection,
                currentType,
                currentAttribute,
                currentSort
            });

            if (coursesData.length > 0) {
                console.log('第一个课程数据:', coursesData[0]);
            }

            if (allCoursesData.length > 0) {
                console.log('第一个课程选择器数据:', allCoursesData[0]);
            }

            return {
                coursesData: coursesData.length,
                allCoursesData: allCoursesData.length,
                filteredCourses: filteredCourses.length,
                filters: {
                    currentCategory,
                    currentCourse,
                    currentSection,
                    currentType,
                    currentAttribute,
                    currentSort
                }
            };
        }

        // 将调试函数暴露到全局，方便在控制台调用
        window.debugDataStatus = debugDataStatus;

        // 显示错误信息
        function showError(message) {
            console.error(message);
            showToast(message, 'error');
        }

        // 显示加载状态
        function showLoading() {
            console.log('Loading...');
            // 这里可以添加加载动画
        }

        // 隐藏加载状态
        function hideLoading() {
            console.log('Loading complete');
            // 这里可以隐藏加载动画
        }

        // 记录课程点击 - 与首页保持一致
        function recordItemClick(itemId, itemType) {
            // 异步记录点击，不阻塞页面跳转
            const clickUrl = itemType === 'course' ?
                baseurl + "/course/click/" + itemId :
                baseurl + "/redbook/click/" + itemId;

            $.ajax({
                url: clickUrl,
                type: 'GET',
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function() {
                    console.log(`${itemType}点击记录成功, ID:`, itemId);
                },
                error: function() {
                    console.log(`${itemType}点击记录失败，但不影响用户体验`);
                }
            });
        }

        // 绑定学习事件 - 与首页保持一致
        function bindLearningEvents(container) {
            if (!container) return;

            const learningItems = container.querySelectorAll('.course-card');
            learningItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的是链接，让默认行为处理
                    if (this.tagName.toLowerCase() === 'a') {
                        return true;
                    }

                    // 否则手动处理跳转
                    const itemId = this.dataset.id;
                    const itemType = this.dataset.type || 'course';

                    if (itemId) {
                        recordItemClick(itemId, itemType);

                        // 跳转到详情页
                        const detailUrl = itemType === 'course' ?
                            `course-detail.html?id=${itemId}` :
                            `redbook-detail.html?id=${itemId}`;
                        window.location.href = detailUrl;
                    }
                });
            });
        }



        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html> 