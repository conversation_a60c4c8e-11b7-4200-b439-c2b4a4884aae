<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>系统设置 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .settings-container {
            padding: 20px;
            background: #f8fafc;
            min-height: calc(100vh - 140px);
        }

        .settings-section {
            background: white;
            border-radius: 20px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }

        .section-title {
            padding: 20px 20px 16px;
            font-size: 18px;
            font-weight: 700;
            color: #2d3748;
            letter-spacing: 0.5px;
        }

        .settings-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid #f7fafc;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .settings-item:last-child {
            border-bottom: none;
        }

        .settings-item:active {
            background: #f7fafc;
        }

        .settings-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
        }

        .settings-icon.profile {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .settings-icon.notification {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .settings-icon.privacy {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .settings-icon.theme {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .settings-icon.language {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .settings-icon.cache {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .settings-icon.about {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .settings-content {
            flex: 1;
        }

        .settings-title {
            font-size: 16px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .settings-desc {
            font-size: 13px;
            color: #718096;
            line-height: 1.4;
        }

        .settings-value {
            font-size: 14px;
            color: #a0aec0;
            margin-right: 8px;
        }

        .settings-arrow {
            color: #cbd5e0;
            font-size: 16px;
        }

        .toggle-switch {
            position: relative;
            width: 48px;
            height: 28px;
            background: #e2e8f0;
            border-radius: 14px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 24px;
            height: 24px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(20px);
        }

        .profile-edit-section {
            padding: 0 20px 20px;
        }

        .profile-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            border-bottom: 1px solid #f7fafc;
        }

        .profile-item:last-child {
            border-bottom: none;
        }

        .profile-label {
            width: 80px;
            font-size: 14px;
            color: #4a5568;
            font-weight: 500;
        }

        .profile-value {
            flex: 1;
            font-size: 14px;
            color: #2d3748;
            padding: 8px 12px;
            background: #f7fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }

        .cache-info {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: #f7fafc;
            margin: 0 20px 20px;
            border-radius: 12px;
        }

        .cache-size {
            font-size: 14px;
            color: #4a5568;
        }

        .clear-cache-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .clear-cache-btn:active {
            transform: translateY(1px);
        }

        .version-info {
            text-align: center;
            padding: 20px;
            color: #a0aec0;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">系统设置</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="settings-container">
        <!-- 个人信息设置 -->
        <div class="settings-section">
            <div class="section-title">个人信息</div>
            <div class="settings-item" onclick="editProfile()">
                <div class="settings-icon profile">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L19 5V4C19 2.9 18.1 2 17 2H7C5.9 2 5 2.9 5 4V5L3 7V9H5V20C5 21.1 5.9 22 7 22H17C18.1 22 19 21.1 19 20V9H21Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">编辑个人资料</div>
                    <div class="settings-desc">修改头像、昵称等个人信息</div>
                </div>
                <span class="settings-arrow">›</span>
            </div>
        </div>

        <!-- 通知设置 -->
        <div class="settings-section">
            <div class="section-title">通知设置</div>
            <div class="settings-item">
                <div class="settings-icon notification">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.36 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.63 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">推送通知</div>
                    <div class="settings-desc">接收学习提醒和系统通知</div>
                </div>
                <div class="toggle-switch active" id="notificationToggle" onclick="toggleNotification()">
                    <div class="toggle-slider"></div>
                </div>
            </div>
            <div class="settings-item">
                <div class="settings-icon notification">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM13 17H11V15H13V17ZM13 13H11V7H13V13Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">学习提醒</div>
                    <div class="settings-desc">定时提醒学习任务和课程</div>
                </div>
                <div class="toggle-switch" id="reminderToggle" onclick="toggleReminder()">
                    <div class="toggle-slider"></div>
                </div>
            </div>
        </div>

        <!-- 隐私设置 -->
        <div class="settings-section">
            <div class="section-title">隐私与安全</div>
            <div class="settings-item" onclick="changePassword()">
                <div class="settings-icon privacy">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M18 8H17V6C17 3.24 14.76 1 12 1C9.24 1 7 3.24 7 6V8H6C4.9 8 4 8.9 4 10V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V10C20 8.9 19.1 8 18 8ZM12 17C10.9 17 10 16.1 10 15C10 13.9 10.9 13 12 13C13.1 13 14 13.9 14 15C14 16.1 13.1 17 12 17ZM15.1 8H8.9V6C8.9 4.29 10.29 2.9 12 2.9C13.71 2.9 15.1 4.29 15.1 6V8Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">修改密码</div>
                    <div class="settings-desc">更改登录密码</div>
                </div>
                <span class="settings-arrow">›</span>
            </div>
        </div>

        <!-- 应用设置 -->
        <div class="settings-section">
            <div class="section-title">应用设置</div>
            <div class="settings-item" onclick="selectTheme()">
                <div class="settings-icon theme">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 17.52 6.48 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM12 20V4C16.41 4 20 7.59 20 12C20 16.41 16.41 20 12 20Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">主题模式</div>
                    <div class="settings-desc">选择浅色或深色主题</div>
                </div>
                <span class="settings-value" id="themeValue">浅色</span>
                <span class="settings-arrow">›</span>
            </div>
            <div class="settings-item" onclick="selectLanguage()">
                <div class="settings-icon language">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12.87 15.07L10.33 12.56L10.36 12.53C12.1 10.59 13.34 8.36 14.07 6H17V4H10V2H8V4H1V6H12.17C11.5 7.92 10.44 9.75 9 11.35C8.07 10.32 7.3 9.19 6.69 8H4.69C5.42 9.63 6.42 11.17 7.67 12.56L2.58 17.58L4 19L9 14L12.11 17.11L12.87 15.07ZM18.5 10H16.5L12 22H14L15.12 19H19.87L21 22H23L18.5 10ZM15.88 17L17.5 12.67L19.12 17H15.88Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">语言设置</div>
                    <div class="settings-desc">选择应用显示语言</div>
                </div>
                <span class="settings-value" id="languageValue">简体中文</span>
                <span class="settings-arrow">›</span>
            </div>
        </div>

        <!-- 存储管理 -->
        <div class="settings-section">
            <div class="section-title">存储管理</div>
            <div class="cache-info">
                <div class="settings-icon cache">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M6 2C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2H6ZM13 3.5L18.5 9H13V3.5Z"/>
                    </svg>
                </div>
                <div class="cache-size">缓存大小：<span id="cacheSize">计算中...</span></div>
                <button class="clear-cache-btn" onclick="clearCache()">清理缓存</button>
            </div>
        </div>

        <!-- 关于应用 -->
        <div class="settings-section">
            <div class="section-title">关于应用</div>
            <div class="settings-item" onclick="showAbout()">
                <div class="settings-icon about">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M11,9H13V7H11M12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20ZM12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2ZM11,17H13V11H11V17Z"/>
                    </svg>
                </div>
                <div class="settings-content">
                    <div class="settings-title">关于我们</div>
                    <div class="settings-desc">版本信息和使用条款</div>
                </div>
                <span class="settings-arrow">›</span>
            </div>
        </div>

        <!-- 版本信息 -->
        <div class="version-info">
            <div>思政一体化平台 移动版</div>
            <div>版本 1.0.0</div>
            <div style="margin-top: 8px;">© 2024 思政一体化平台</div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 加载设置数据
            loadSettings();
            
            // 计算缓存大小
            calculateCacheSize();
        });

        function loadSettings() {
            // 从本地存储加载设置
            const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');
            
            // 更新通知开关状态
            const notificationToggle = document.getElementById('notificationToggle');
            if (settings.notifications === false) {
                notificationToggle.classList.remove('active');
            }
            
            // 更新提醒开关状态
            const reminderToggle = document.getElementById('reminderToggle');
            if (settings.reminders === true) {
                reminderToggle.classList.add('active');
            }
            
            // 更新主题显示
            const themeValue = document.getElementById('themeValue');
            themeValue.textContent = settings.theme === 'dark' ? '深色' : '浅色';
            
            // 更新语言显示
            const languageValue = document.getElementById('languageValue');
            languageValue.textContent = settings.language || '简体中文';
        }

        function toggleNotification() {
            const toggle = document.getElementById('notificationToggle');
            toggle.classList.toggle('active');
            
            const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');
            settings.notifications = toggle.classList.contains('active');
            localStorage.setItem('appSettings', JSON.stringify(settings));
            
            MobileUtils.showToast(settings.notifications ? '已开启推送通知' : '已关闭推送通知');
        }

        function toggleReminder() {
            const toggle = document.getElementById('reminderToggle');
            toggle.classList.toggle('active');
            
            const settings = JSON.parse(localStorage.getItem('appSettings') || '{}');
            settings.reminders = toggle.classList.contains('active');
            localStorage.setItem('appSettings', JSON.stringify(settings));
            
            MobileUtils.showToast(settings.reminders ? '已开启学习提醒' : '已关闭学习提醒');
        }

        function editProfile() {
            MobileUtils.showToast('个人资料编辑功能开发中');
        }

        function changePassword() {
            MobileUtils.showToast('密码修改功能开发中');
        }

        function selectTheme() {
            MobileUtils.showToast('主题切换功能开发中');
        }

        function selectLanguage() {
            MobileUtils.showToast('语言设置功能开发中');
        }

        function calculateCacheSize() {
            // 模拟计算缓存大小
            setTimeout(() => {
                const cacheSize = (Math.random() * 50 + 10).toFixed(1);
                document.getElementById('cacheSize').textContent = cacheSize + ' MB';
            }, 1000);
        }

        function clearCache() {
            const button = event.target;
            button.textContent = '清理中...';
            button.disabled = true;
            
            setTimeout(() => {
                button.textContent = '清理缓存';
                button.disabled = false;
                document.getElementById('cacheSize').textContent = '2.1 MB';
                MobileUtils.showToast('缓存清理完成');
            }, 2000);
        }

        function showAbout() {
            MobileUtils.showToast('关于页面开发中');
        }
    </script>
</body>
</html>
