<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>考试成绩 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .achievements-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .stats-overview {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        .filter-section {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .filter-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 4px;
            gap: 4px;
        }

        .filter-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            border-radius: 6px;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            background: transparent;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-tab.active {
            background: #c00714;
            color: white;
        }

        .achievements-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .achievement-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }

        .achievement-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        }

        .achievement-item.excellent {
            border-left: 4px solid #28a745;
        }

        .achievement-item.good {
            border-left: 4px solid #007bff;
        }

        .achievement-item.average {
            border-left: 4px solid #ffc107;
        }

        .achievement-item.poor {
            border-left: 4px solid #dc3545;
        }

        .achievement-header {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            margin-bottom: 12px;
        }

        .exam-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .achievement-info {
            flex: 1;
        }

        .exam-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
            line-height: 1.4;
        }

        .exam-subject {
            font-size: 13px;
            color: #666;
            margin-bottom: 4px;
        }

        .exam-time {
            font-size: 12px;
            color: #999;
        }

        .score-badge {
            position: absolute;
            top: 12px;
            right: 12px;
            padding: 6px 12px;
            border-radius: 12px;
            font-size: 14px;
            font-weight: 600;
            color: white;
        }

        .score-badge.excellent {
            background: #28a745;
        }

        .score-badge.good {
            background: #007bff;
        }

        .score-badge.average {
            background: #ffc107;
            color: #333;
        }

        .score-badge.poor {
            background: #dc3545;
        }

        .achievement-details {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            font-size: 12px;
            margin-top: 12px;
        }

        .detail-item {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .detail-value {
            font-weight: 600;
            color: #333;
            margin-bottom: 2px;
        }

        .detail-label {
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }

        .empty-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }

        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">考试成绩</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="achievements-container">
        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number" id="totalExams">0</div>
                    <div class="stat-label">考试总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="avgScore">0</div>
                    <div class="stat-label">平均分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number" id="passRate">0%</div>
                    <div class="stat-label">及格率</div>
                </div>
            </div>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-tabs">
                <button class="filter-tab active" data-filter="all">全部</button>
                <button class="filter-tab" data-filter="excellent">优秀</button>
                <button class="filter-tab" data-filter="good">良好</button>
                <button class="filter-tab" data-filter="average">一般</button>
                <button class="filter-tab" data-filter="poor">待提高</button>
            </div>
        </div>

        <!-- 成绩列表 -->
        <div class="achievements-list" id="achievementsList">
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <p>正在加载考试成绩...</p>
            </div>
        </div>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let currentFilter = 'all';
        let achievementsData = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化筛选功能
            initFilters();
            
            // 加载考试成绩数据
            loadAchievements();
        });

        function initFilters() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 更新筛选状态
                    currentFilter = filter;
                    renderAchievements();
                });
            });
        }

        function loadAchievements() {
            // 获取考试成绩数据 - 使用PC端相同的接口
            $.ajax({
                url: baseurl + "/achievement/weblist",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 100
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        achievementsData = res.data.list || [];
                        updateStatsOverview();
                        renderAchievements();
                    } else {
                        // 如果没有数据，尝试使用模拟数据
                        loadMockAchievements();
                    }
                },
                error: (err) => {
                    console.error('加载考试成绩失败:', err);
                    // 加载模拟数据
                    loadMockAchievements();
                }
            });
        }

        function loadMockAchievements() {
            // 模拟考试成绩数据
            achievementsData = [
                {
                    id: '1',
                    score: 95,
                    keguanTotalScore: 45,
                    zhuguanScore: 50,
                    cmsTestPaper: { name: '思政理论基础测试' },
                    subjectName: '思想政治理论',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    score: 82,
                    keguanTotalScore: 40,
                    zhuguanScore: 42,
                    cmsTestPaper: { name: '马克思主义基本原理' },
                    subjectName: '马克思主义理论',
                    createdAt: new Date(Date.now() - 86400000).toISOString()
                },
                {
                    id: '3',
                    score: 76,
                    keguanTotalScore: 35,
                    zhuguanScore: 41,
                    cmsTestPaper: { name: '中国近现代史纲要' },
                    subjectName: '中国近现代史',
                    createdAt: new Date(Date.now() - 172800000).toISOString()
                }
            ];
            updateStatsOverview();
            renderAchievements();
        }

        function updateStatsOverview() {
            const totalExams = achievementsData.length;
            let totalScore = 0;
            let passCount = 0;

            achievementsData.forEach(item => {
                const score = parseInt(item.score) || 0;
                totalScore += score;
                if (score >= 60) passCount++;
            });

            const avgScore = totalExams > 0 ? Math.round(totalScore / totalExams) : 0;
            const passRate = totalExams > 0 ? Math.round((passCount / totalExams) * 100) : 0;

            document.getElementById('totalExams').textContent = totalExams;
            document.getElementById('avgScore').textContent = avgScore;
            document.getElementById('passRate').textContent = passRate + '%';
        }

        function renderAchievements() {
            const achievementsList = document.getElementById('achievementsList');
            
            let filteredData = achievementsData;
            if (currentFilter !== 'all') {
                filteredData = achievementsData.filter(item => getScoreLevel(parseInt(item.score)) === currentFilter);
            }
            
            if (filteredData.length === 0) {
                achievementsList.innerHTML = '<div class="empty-state"><div class="empty-icon">📊</div><div>暂无相关成绩数据</div></div>';
                return;
            }

            let html = '';
            filteredData.forEach(item => {
                const score = parseInt(item.score) || 0;
                const scoreLevel = getScoreLevel(score);
                const scoreLevelText = getScoreLevelText(scoreLevel);
                
                html += `
                    <div class="achievement-item ${scoreLevel}" onclick="viewAchievementDetail('${item.id}')">
                        <div class="score-badge ${scoreLevel}">${score}分</div>
                        <div class="achievement-header">
                            <div class="exam-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                                    <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z"/>
                                </svg>
                            </div>
                            <div class="achievement-info">
                                <div class="exam-title">${item.cmsTestPaper?.name || '考试'}</div>
                                <div class="exam-subject">${item.subjectName || '未知科目'}</div>
                                <div class="exam-time">${MobileUtils.formatTime(item.createdAt)}</div>
                            </div>
                        </div>
                        <div class="achievement-details">
                            <div class="detail-item">
                                <div class="detail-value">${item.keguanTotalScore || 0}分</div>
                                <div class="detail-label">客观题</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-value">${item.zhuguanScore || 0}分</div>
                                <div class="detail-label">主观题</div>
                            </div>
                            <div class="detail-item">
                                <div class="detail-value">${scoreLevelText}</div>
                                <div class="detail-label">等级评定</div>
                            </div>
                        </div>
                    </div>
                `;
            });

            achievementsList.innerHTML = html;
        }

        function getScoreLevel(score) {
            if (score >= 90) return 'excellent';
            if (score >= 80) return 'good';
            if (score >= 60) return 'average';
            return 'poor';
        }

        function getScoreLevelText(level) {
            const levelMap = {
                'excellent': '优秀',
                'good': '良好',
                'average': '一般',
                'poor': '待提高'
            };
            return levelMap[level] || '未知';
        }

        function viewAchievementDetail(achievementId) {
            // 跳转到成绩详情页
            window.location.href = `achievement-detail.html?id=${achievementId}`;
        }
    </script>
</body>
</html>
