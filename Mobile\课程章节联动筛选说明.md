# 课程章节联动筛选功能说明

## 📋 功能概述

根据您的要求，我已经将筛选面板的左侧改为"课程分类"，右侧改为"章节分类"，并实现了选择课程后自动加载对应章节的联动功能。

## 🎯 核心改进

### 1. **筛选面板重构**
```html
<!-- 主要筛选：课程分类和章节分类 -->
<div class="filter-row">
    <div class="filter-item">
        <label class="filter-title">课程分类</label>
        <select class="filter-select" id="courseSelect">
            <option value="all">全部课程</option>
            <!-- 动态加载课程列表 -->
        </select>
    </div>
    <div class="filter-item">
        <label class="filter-title">章节分类</label>
        <select class="filter-select" id="sectionSelect" disabled>
            <option value="all">全部章节</option>
            <!-- 根据选择的课程动态加载 -->
        </select>
    </div>
</div>
```

### 2. **联动逻辑实现**
- **初始状态**：章节选择器处于禁用状态
- **选择课程**：自动启用章节选择器并加载对应章节
- **重置功能**：重置时章节选择器回到禁用状态

## 📊 数据结构说明

### API接口数据结构
**接口**: `GET /project/section/list/tree`

**返回数据结构**:
```json
{
  "code": "200",
  "data": [
    {
      "id": "course1",
      "name": "思想道德与法治",  // 这个作为课程名称
      "level": 0,
      "children": [
        {
          "id": "section1-1",
          "name": "第一章 人生的青春之问",
          "level": 1,
          "children": []
        },
        {
          "id": "section1-2",
          "name": "第二章 坚定理想信念",
          "level": 1,
          "children": []
        }
      ]
    },
    {
      "id": "course2",
      "name": "中国近现代史纲要",
      "level": 0,
      "children": [...]
    }
  ]
}
```

**数据提取逻辑**:
- `data[0].name` → 第一个课程的名称
- `data[1].name` → 第二个课程的名称
- `data[i].children` → 对应课程的章节列表

## 🔧 技术实现

### 1. **数据结构优化**
```javascript
// 新增数据存储
let allCoursesData = []; // 存储所有课程数据用于筛选面板
let currentCourse = 'all'; // 当前选择的课程

// 筛选状态
let currentCategory = 'all';
let currentCourse = 'all';    // 新增
let currentSection = 'all';
let currentType = 'all';
let currentSort = 'createTime-desc';
```

### 2. **课程数据加载**
```javascript
// 加载所有课程数据（用于筛选面板）
function loadAllCourses() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/project/section/list/tree",
            success: function(res) {
                if (res.code == '200' && res.data && Array.isArray(res.data)) {
                    // 从 section tree 数据中提取课程信息
                    allCoursesData = extractCoursesFromSectionTree(res.data);
                    updateCourseOptions(); // 更新课程选择器
                    resolve(allCoursesData);
                }
            }
        });
    });
}

// 从章节树数据中提取课程信息
function extractCoursesFromSectionTree(sectionTreeData) {
    const courses = [];

    // 遍历章节树的根节点，每个根节点代表一个课程
    sectionTreeData.forEach((rootSection, index) => {
        if (rootSection.name) {
            courses.push({
                id: rootSection.id || `course_${index}`,
                title: rootSection.name,  // 使用 data[0].name 作为课程名称
                sectionId: rootSection.id,
                category: 'course',
                level: rootSection.level || 0,
                children: rootSection.children || []
            });
        }
    });

    return courses;
}
```

### 3. **章节联动加载**
```javascript
// 根据课程ID加载对应的章节
function loadCourseSections(courseId) {
    return new Promise((resolve, reject) => {
        // 首先从已加载的课程数据中查找对应的课程
        const selectedCourse = allCoursesData.find(course => course.id === courseId);

        if (selectedCourse && selectedCourse.children && selectedCourse.children.length > 0) {
            // 如果找到课程且有子章节，直接使用
            const courseSections = flattenSectionTree(selectedCourse.children);
            resolve(courseSections);
        } else {
            // 否则重新请求章节数据
            $.ajax({
                url: baseurl + "/project/section/list/tree",
                success: function(res) {
                    // 查找指定课程的章节
                    const targetCourse = res.data.find(course => course.id === courseId);
                    if (targetCourse && targetCourse.children) {
                        const courseSections = flattenSectionTree(targetCourse.children);
                        resolve(courseSections);
                    }
                }
            });
        }
    });
}
```

### 4. **联动事件处理**
```javascript
// 课程选择器事件 - 选择课程后加载对应章节
courseSelect.addEventListener('change', function() {
    const selectedCourseId = this.value;
    currentCourse = selectedCourseId;
    
    // 重置章节选择
    currentSection = 'all';
    
    // 根据选择的课程更新章节选项
    updateSectionOptions(selectedCourseId);
    
    // 显示加载提示
    if (selectedCourseId !== 'all') {
        const courseName = this.options[this.selectedIndex].text;
        showToast(`已选择课程：${courseName}，正在加载章节...`, 'info');
    }
});
```

## 🎨 界面设计

### 筛选面板布局
```
┌─────────────────────────────────┐
│  🔍 [搜索框]           [筛选]    │  ← 搜索容器
├─────────────────────────────────┤
│  [视频] [文档] [PPT] [必修]...   │  ← 快速分类
├─────────────────────────────────┤
│  ⚙️ 课程章节筛选面板             │
│  课程: [全部课程 ▼] 章节: [禁用] │  ← 主要筛选
│  排序: [最新发布 ▼] 类型: [全部 ▼] │  ← 辅助筛选
│  [重置筛选]       [应用筛选]     │
└─────────────────────────────────┘
```

### 状态变化
1. **初始状态**：
   - 课程选择器：显示"全部课程"
   - 章节选择器：禁用状态，显示"全部章节"

2. **选择课程后**：
   - 课程选择器：显示选中的课程名称
   - 章节选择器：启用状态，显示该课程的章节列表

3. **选择章节后**：
   - 章节选择器：显示选中的章节名称
   - 系统：根据课程+章节进行精确筛选

## 🔄 联动流程

### 用户操作流程
1. **打开筛选面板** → 点击筛选按钮
2. **选择课程** → 从课程下拉列表中选择具体课程
3. **自动加载章节** → 系统自动加载该课程的章节列表
4. **选择章节** → 从章节下拉列表中选择具体章节
5. **应用筛选** → 点击"应用筛选"按钮生效

### 系统处理流程
1. **课程选择事件触发**
2. **发送API请求** → `/project/section/list/tree?courseId=xxx`
3. **处理章节数据** → 扁平化章节树结构
4. **更新章节选择器** → 填充章节选项并启用
5. **显示用户反馈** → Toast提示选择状态

## 📊 功能特性

### ✅ 已实现功能
- **课程列表加载**：自动加载所有可用课程
- **章节联动加载**：根据课程ID动态加载章节
- **智能状态管理**：章节选择器的启用/禁用控制
- **实时用户反馈**：选择操作的Toast提示
- **数据缓存优化**：避免重复加载相同数据
- **错误处理机制**：网络异常时的降级处理
- **重置功能完善**：一键重置所有筛选条件

### 🎯 筛选逻辑
```javascript
function filterCourses() {
    filteredCourses = coursesData.filter(course => {
        // 分类筛选
        const matchCategory = currentCategory === 'all' || 
            course.category === currentCategory;
        
        // 课程筛选 - 新增
        const matchCourse = currentCourse === 'all' || 
            course.id === currentCourse ||
            course.courseId === currentCourse;
        
        // 章节筛选
        const matchSection = currentSection === 'all' || 
            course.sectionId === currentSection;
        
        // 其他筛选条件...
        
        return matchCategory && matchCourse && matchSearch && 
               matchSection && matchType;
    });
}
```

## 🚀 使用说明

### 开发者说明
1. **API接口**：使用 `/project/section/list/tree` 接口获取课程和章节数据
2. **数据格式**：
   - 根节点 (`level: 0`) 作为课程，使用 `name` 字段作为课程名称
   - 子节点 (`level: 1+`) 作为章节，支持多级嵌套结构
   - 必需字段：`id`、`name`、`level`、`children`
3. **数据提取**：从章节树的根节点提取课程信息，从 `children` 提取章节信息
4. **错误处理**：网络异常时会使用默认课程和章节数据
5. **性能优化**：课程数据会在页面加载时一次性获取并缓存

### 用户使用指南
1. **基础筛选**：直接使用快速分类标签
2. **精确筛选**：打开筛选面板，选择具体课程和章节
3. **组合筛选**：结合搜索、课程、章节、排序等多个条件
4. **重置筛选**：点击"重置筛选"按钮清除所有条件

## 💡 优化建议

### 短期优化
1. **章节搜索**：在章节选择器中添加搜索功能
2. **收藏课程**：支持收藏常用课程到快速访问列表
3. **历史记录**：记住用户最近选择的课程和章节

### 长期优化
1. **智能推荐**：根据用户学习历史推荐相关课程
2. **个性化筛选**：保存用户常用的筛选组合
3. **批量操作**：支持同时选择多个课程或章节

---

## 📞 技术支持

如需进一步优化或有任何问题，请随时联系开发团队。

**更新时间**: 2025-06-19  
**版本**: v4.0 - 课程章节联动筛选版
