<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选条件优化展示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }
        
        .demo-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 8px 0 0 0;
        }
        
        .comparison {
            display: flex;
            flex-direction: column;
        }
        
        .comparison-item {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .comparison-item:last-child {
            border-bottom: none;
        }
        
        .comparison-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-old {
            background: #ffeaa7;
            color: #d63031;
        }
        
        .status-new {
            background: #d1f2eb;
            color: #00b894;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .feature-list li {
            padding: 6px 0;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .feature-list li:before {
            content: "•";
            color: #c00714;
            font-weight: bold;
            font-size: 16px;
        }
        
        .improvement-list li:before {
            content: "✅";
            font-size: 14px;
        }
        
        .demo-actions {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
        }
        
        .demo-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            background: #a00610;
            transform: translateY(-1px);
        }
        
        .highlight-box {
            background: #f8f9fa;
            border-left: 4px solid #c00714;
            padding: 16px;
            margin: 16px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .highlight-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .highlight-text {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">📱 筛选条件优化</h1>
            <p class="demo-subtitle">更简洁、更高效的移动端筛选体验</p>
        </div>
        
        <div class="comparison">
            <div class="comparison-item">
                <div class="comparison-title">
                    🔍 搜索体验优化
                    <span class="status-badge status-new">已优化</span>
                </div>
                <ul class="feature-list improvement-list">
                    <li>搜索框与筛选按钮并排布局，节省空间</li>
                    <li>筛选按钮采用图标设计，更直观</li>
                    <li>搜索框聚焦时有红色主题色高亮</li>
                    <li>支持实时搜索和API搜索降级</li>
                </ul>
            </div>
            
            <div class="comparison-item">
                <div class="comparison-title">
                    🏷️ 分类标签简化
                    <span class="status-badge status-new">已优化</span>
                </div>
                <ul class="feature-list improvement-list">
                    <li>快速分类标签更小巧，减少视觉负担</li>
                    <li>采用红色主题色突出选中状态</li>
                    <li>支持横向滚动，适配更多分类</li>
                    <li>点击时有即时反馈和提示</li>
                </ul>
            </div>
            
            <div class="comparison-item">
                <div class="comparison-title">
                    ⚙️ 筛选面板重构
                    <span class="status-badge status-new">全新设计</span>
                </div>
                <ul class="feature-list improvement-list">
                    <li>从复杂的多级筛选简化为两个选择器</li>
                    <li>排序和类型筛选一目了然</li>
                    <li>面板采用卡片式设计，更现代</li>
                    <li>重置和应用按钮布局更合理</li>
                </ul>
            </div>
            
            <div class="comparison-item">
                <div class="comparison-title">
                    📱 移动端适配
                    <span class="status-badge status-new">完全适配</span>
                </div>
                <ul class="feature-list improvement-list">
                    <li>触摸友好的按钮尺寸和间距</li>
                    <li>滑动动画和视觉反馈</li>
                    <li>自适应不同屏幕尺寸</li>
                    <li>优化的交互逻辑和用户体验</li>
                </ul>
            </div>
        </div>
        
        <div class="highlight-box">
            <div class="highlight-title">🎯 核心改进</div>
            <div class="highlight-text">
                将原本占用大量空间的复杂筛选界面，优化为紧凑的搜索+快速分类+简化筛选的三层结构，
                大幅提升了空间利用率和用户体验。
            </div>
        </div>
        
        <div class="demo-actions">
            <a href="pages/course-list.html" class="demo-btn">🚀 体验优化后的筛选</a>
        </div>
    </div>
    
    <div style="text-align: center; padding: 20px; color: #999; font-size: 12px;">
        💡 优化重点：简洁性、易用性、空间效率
    </div>
</body>
</html>
