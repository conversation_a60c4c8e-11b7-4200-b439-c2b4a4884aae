# 筛选功能问题排查指南

## 🚨 问题现象
筛选应用后，下方没有出现对应的列表内容

## 🔍 排查步骤

### 第一步：检查浏览器控制台
1. 按 `F12` 打开开发者工具
2. 切换到 `Console` 标签页
3. 刷新页面，观察是否有错误信息
4. 尝试使用筛选功能，观察控制台输出

### 第二步：检查数据加载状态
在控制台中输入以下命令检查数据状态：
```javascript
debugDataStatus()
```

**正常情况应该看到：**
- `coursesData 长度: > 0` （有课程数据）
- `allCoursesData 长度: > 0` （有课程选择器数据）
- `filteredCourses 长度: >= 0` （筛选结果）

### 第三步：检查筛选逻辑
观察控制台中的筛选日志：
- `开始筛选课程，原始数据数量: X`
- `当前筛选条件: {...}`
- `第一个课程的匹配情况: {...}`
- `筛选后的课程数量: X`

### 第四步：检查渲染过程
观察控制台中的渲染日志：
- `开始渲染课程，筛选后数量: X`
- `有课程数据，开始渲染` 或 `没有课程数据，显示空状态`

## 🛠️ 常见问题及解决方案

### 问题1：数据加载失败
**症状：** `coursesData 长度: 0`

**可能原因：**
- API接口返回错误
- 认证信息过期
- 网络连接问题

**解决方法：**
1. 检查登录状态
2. 刷新页面重新加载
3. 检查网络连接

### 问题2：筛选条件过于严格
**症状：** `筛选后的课程数量: 0`

**可能原因：**
- 选择的课程和章节组合没有匹配的内容
- 筛选逻辑与数据字段不匹配

**解决方法：**
1. 点击"重置筛选"按钮
2. 逐个设置筛选条件，观察结果变化
3. 检查课程数据的字段结构

### 问题3：课程选择器为空
**症状：** 课程选择器只显示"全部课程"

**可能原因：**
- `/project/section/list/tree` API返回空数据
- 数据提取逻辑有误

**解决方法：**
1. 检查API响应数据
2. 使用默认数据进行测试

### 问题4：章节联动不工作
**症状：** 选择课程后章节选择器仍然禁用

**可能原因：**
- 课程数据中缺少children字段
- 章节加载失败

**解决方法：**
1. 检查课程数据的children字段
2. 查看章节加载的错误信息

## 🔧 手动测试步骤

### 测试1：基础功能
1. 打开课程列表页面
2. 等待页面加载完成
3. 检查是否显示课程卡片
4. 尝试搜索功能

### 测试2：快速分类
1. 点击不同的快速分类标签
2. 观察课程列表是否变化
3. 检查控制台的筛选日志

### 测试3：筛选面板
1. 点击筛选按钮打开面板
2. 选择不同的课程
3. 观察章节选择器是否启用
4. 选择章节后点击"应用筛选"
5. 检查结果是否正确

### 测试4：重置功能
1. 设置一些筛选条件
2. 点击"重置筛选"按钮
3. 检查所有条件是否重置
4. 检查课程列表是否恢复

## 📞 获取技术支持

如果按照上述步骤仍无法解决问题，请提供以下信息：

1. **浏览器信息**：浏览器类型和版本
2. **错误截图**：控制台错误信息截图
3. **操作步骤**：详细的操作步骤
4. **数据状态**：`debugDataStatus()` 的输出结果
5. **网络状态**：是否能正常访问其他功能

## 🚀 快速修复

如果遇到问题，可以尝试以下快速修复方法：

1. **刷新页面**：`Ctrl + F5` 强制刷新
2. **清除缓存**：清除浏览器缓存和Cookie
3. **重新登录**：退出后重新登录
4. **检查网络**：确保网络连接正常
5. **使用默认数据**：如果API有问题，系统会使用默认数据

---

**最后更新时间**: 2025-06-19  
**版本**: v1.0 - 筛选问题排查指南
