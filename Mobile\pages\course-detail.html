<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>课程详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    <link rel="stylesheet" href="../css/learning-styles.css">
    
    <style>
        .detail-container {
            background: #f5f5f5;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        /* 资源播放区域样式 */
        .resource-player {
            background: #000;
            position: relative;
            width: 100%;
            height: 280px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            overflow: hidden;
        }

        @media (max-width: 480px) {
            .resource-player {
                height: 240px;
            }
        }

        /* 视频播放器样式 */
        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
            overflow: hidden;
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.3);
            opacity: 1;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .video-overlay.hidden {
            opacity: 0;
        }

        .center-play-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #333;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: all;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
        }

        .center-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
        }

        .center-play-btn:active {
            transform: scale(0.98);
        }

        .video-controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 8px 12px;
            border-radius: 20px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-container:hover .video-controls {
            opacity: 1;
        }

        .play-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #c00714;
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        .fullscreen-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .player-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .pdf-embed {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .doc-viewer {
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }

        .ppt-viewer {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: white;
        }

        .video-player {
            width: 100%;
            height: 100%;
            background: #000;
        }

        .placeholder-content {
            text-align: center;
            color: #999;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .placeholder-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .placeholder-subtitle {
            font-size: 14px;
            opacity: 0.7;
        }

        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .play-button {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }

        .resource-info {
            display: none; /* 隐藏资源信息 */
        }
        
        .detail-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
        }
        
        .detail-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            padding: 0 60px;
        }
        
        .course-info {
            background: white;
            margin: 20px;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .course-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #333;
            padding: 20px;
            text-align: center;
            position: relative;
            border-bottom: 1px solid #e9ecef;
        }

        .course-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
            color: #c00714;
        }

        .course-content {
            padding: 20px;
        }
        
        .course-meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .course-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .course-teacher {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .course-category {
            display: inline-block;
            background: #f8f9fa;
            color: #c00714;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            font-weight: 500;
        }

        .course-stats {
            display: flex;
            gap: 15px;
            font-size: 13px;
            color: #666;
            justify-content: center;
        }

        .course-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #ffc107;
            font-size: 14px;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .course-description {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .description-content {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .course-chapters {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chapter-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .chapter-item:last-child {
            border-bottom: none;
        }

        .chapter-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .chapter-actions button {
            transition: all 0.3s ease;
        }

        .chapter-actions button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .chapter-number {
            width: 30px;
            height: 30px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-right: 12px;
        }
        
        .chapter-info {
            flex: 1;
        }
        
        .chapter-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .chapter-duration {
            font-size: 12px;
            color: #999;
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #c00714;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .retry-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <!-- 头部 -->
        <div class="detail-header">
            <button class="back-btn" onclick="goBack()">
                ←
            </button>
            <h1 class="detail-title">课程详情</h1>
        </div>
        
        <!-- 内容区域 -->
        <div id="contentArea">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载课程详情...</p>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons" id="actionButtons" style="display: none;">
            <button class="action-btn btn-secondary" id="studyTimerBtn" onclick="toggleStudyTimer()">
                ⏱ 开始计时
            </button>
            <button class="action-btn btn-primary" onclick="showRatingDialog()">
                ⭐ 课程评分
            </button>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        // 全局变量
        let studyStartTime = null;
        let studyTimer = null;
        let totalStudyTime = 0;
        let currentCourseId = null;

        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            // 如果正在学习，显示移动端友好的确认对话框
            if (studyStartTime !== null) {
                showMobileConfirm(
                    '确认返回？',
                    '您的学习记录将被保存',
                    () => {
                        // 确认返回
                        stopStudyTimer();
                        saveLearningRecord();
                        showToast('学习记录已保存', 'success');

                        setTimeout(() => {
                            // 优先返回到课程列表页面
                            const referrer = document.referrer;
                            if (referrer && referrer.includes('course-list.html')) {
                                window.location.href = 'course-list.html';
                            } else if (window.history.length > 1) {
                                window.history.back();
                            } else {
                                window.location.href = 'course-list.html';
                            }
                        }, 300);
                    },
                    () => {
                        // 取消返回，继续学习
                        console.log('用户选择继续学习');
                    }
                );
            } else {
                // 没有在学习，直接返回
                const referrer = document.referrer;
                if (referrer && referrer.includes('course-list.html')) {
                    window.location.href = 'course-list.html';
                } else if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.location.href = 'course-list.html';
                }
            }
        }
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            try {
                const time = new Date(timeStr);
                return time.toLocaleDateString('zh-CN');
            } catch (e) {
                return timeStr;
            }
        }
        
        // 生成星级评分
        function generateStars(score) {
            const fullStars = Math.floor(score);
            const hasHalfStar = score % 1 >= 0.5;
            let stars = '';
            
            for (let i = 0; i < fullStars; i++) {
                stars += '⭐';
            }
            if (hasHalfStar) {
                stars += '⭐';
            }
            
            return stars || '⭐⭐⭐⭐⭐';
        }
        
        // 加载课程详情
        function loadCourseDetail(courseId) {
            if (!courseId) {
                showError('缺少课程ID参数');
                return;
            }

            console.log('=== 开始加载课程详情 ===');
            console.log('课程ID:', courseId);
            console.log('API基础地址:', baseurl);
            console.log('Authorization头:', sessionStorage.getItem("header"));
            console.log('完整API地址:', baseurl + "/course/meta/" + courseId);

            // 使用PC端相同的API接口
            $.ajax({
                url: baseurl + "/course/meta/" + courseId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    console.log('课程详情响应:', res);
                    
                    // 更灵活的响应处理
                    let courseData = null;
                    if (res && res.code == '200' && res.data) {
                        courseData = res.data;
                    } else if (res && res.code == 200 && res.data) {
                        courseData = res.data;
                    } else if (res && typeof res === 'object' && res.id) {
                        // 直接返回课程对象
                        courseData = res;
                    } else if (res && res.success && res.data) {
                        courseData = res.data;
                    } else if (res && Array.isArray(res) && res.length > 0) {
                        courseData = res[0];
                    }
                    
                    if (courseData) {
                        renderCourseDetail(courseData);
                        // 记录课程点击 - 参考PC端
                        recordCourseClick(courseId);
                    } else {
                        console.error('主API无法解析课程数据:', res);
                        console.log('尝试降级策略...');
                        // 尝试降级策略
                        loadCourseDetailWithFallback(courseId);
                    }
                },
                error: (err) => {
                    console.error('主API加载课程详情失败:', err);
                    console.log('网络错误，尝试降级策略...');
                    // 网络错误时也尝试降级策略
                    loadCourseDetailWithFallback(courseId);
                }
            });
        }

        // 记录课程点击 - 参考PC端
        function recordCourseClick(courseId) {
            $.ajax({
                url: baseurl + "/course/click/" + courseId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: function() {
                    console.log('课程点击记录成功');
                },
                error: function() {
                    console.log('课程点击记录失败，但不影响用户体验');
                }
            });
        }

        // 渲染课程详情
        function renderCourseDetail(course) {
            console.log('渲染课程详情数据:', course);
            console.log('课程数据类型:', typeof course);
            console.log('课程数据字段:', Object.keys(course));

            // 如果课程数据为空或无效，显示默认内容
            if (!course || typeof course !== 'object') {
                console.error('课程数据无效:', course);
                renderDefaultCourseContent();
                return;
            }

            // 修复封面图片字段处理 - 参考PC端数据结构
            let imageUrl = '../img/course_default.jpg';
            if (course.coverPath && course.coverPath.length > 0) {
                imageUrl = baseurl + course.coverPath[0];
            } else if (course.covertPath) {
                // 兼容旧的拼写错误字段
                imageUrl = baseurl + course.covertPath;
            }

            // 处理评分数据 - PC端使用 listCourseEvaluate
            let score = 4.5;
            if (course.listCourseEvaluate && course.listCourseEvaluate.length > 3) {
                score = parseFloat(course.listCourseEvaluate[3].zh || 4.5);
            } else if (course.score) {
                score = parseFloat(course.score);
            }
            console.log('处理后的评分:', score);
            const stars = generateStars(score);
            
            // 处理课程基本信息
            const title = course.title || course.titleName || course.name || course.titleZh || course.courseName || '无标题';
            const author = course.author || course.principal || course.teacher || course.lecturer || course.instructorName || '未知';
            const category = course.attachType || course.type || course.category || course.courseType || '课程学习';
            const createTime = course.createTime || course.createdAt || course.publishTime || course.uploadTime;
            const viewCount = course.view || course.studentCount || course.clickCount || course.visits || 0;
            
            console.log('课程基本信息:', { title, author, category, createTime, viewCount });

            // 处理课程介绍
            const introduction = course.introduction || course.content || course.summary || course.description || course.remark || '暂无课程介绍';

            const html = `
                <!-- 资源播放区域 -->
                ${renderResourcePlayer(course)}

                <!-- 课程基本信息 -->
                <div class="course-info">
                    <div class="course-header">
                        <div class="course-icon">🎓</div>
                        <div class="course-title">${title}</div>
                        <div class="course-teacher">讲师：${author}</div>
                        <div class="course-rating">
                            <span>${stars}</span>
                            <span>${score.toFixed(1)}</span>
                        </div>
                    </div>
                    <div class="course-content">
                        <div class="course-category">${category}</div>
                        <div class="course-stats">
                            <span>📅 ${formatTime(createTime)}</span>
                            <span>👥 ${viewCount}人学习</span>
                        </div>
                    </div>
                </div>

                <!-- 课程介绍 -->
                <div class="course-description">
                    <div class="section-title">📋 课程介绍</div>
                    <div class="description-content">
                        ${introduction}
                    </div>
                </div>

                <!-- 课程资源列表已移除，资源在播放器中自动加载 -->
            `;
            
            document.getElementById('contentArea').innerHTML = html;
            document.getElementById('actionButtons').style.display = 'flex';

            // 更新页面标题
            document.title = `${title} - 课程详情`;

            // 自动启动学习计时
            setTimeout(() => {
                autoStartStudyTimer();
            }, 500);
        }

        // 渲染资源播放器
        function renderResourcePlayer(course) {
            // 检查是否有课程资源
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                const firstResource = course.cmsResourcesCourseMetaList[0];
                const resourceType = getResourceType(firstResource.attachPath);
                const resourceName = firstResource.attachName || '课程资源';
                const resourcePath = baseurl + firstResource.attachPath;

                return renderPlayerByType(resourceType, resourcePath, resourceName);
            } else {
                // 没有资源时显示默认播放器
                return `
                    <div class="resource-player">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎓</div>
                            <div class="placeholder-text">暂无课程资源</div>
                            <div class="placeholder-subtitle">请联系管理员添加课程内容</div>
                        </div>
                    </div>
                `;
            }
        }

        // 根据资源类型渲染播放器
        function renderPlayerByType(type, path, name) {
            switch (type.toLowerCase()) {
                case 'pdf':
                    // 自动在PDF查看器中打开
                    setTimeout(() => {
                        openFullPDF(path, name);
                    }, 1000);

                    return `
                        <div class="resource-player">
                            <div class="player-content">
                                <iframe src="https://docs.google.com/viewer?url=${encodeURIComponent(path)}&embedded=true"
                                        style="width: 100%; height: 100%; border: none; background: white;"
                                        onload="console.log('PDF加载完成')"
                                        onerror="console.log('PDF加载失败，将跳转到专用阅读器')">
                                </iframe>
                            </div>
                        </div>
                    `;

                case 'doc':
                case 'docx':
                    return `
                        <div class="resource-player">
                            <div class="doc-viewer">
                                <div class="placeholder-icon">📄</div>
                                <div style="margin-top: 15px; font-size: 16px; color: #666; font-weight: 600;">
                                    ${name}
                                </div>
                                <div style="margin-top: 10px; font-size: 14px; color: #999; line-height: 1.5; text-align: center; padding: 0 20px;">
                                    Word文档需要在外部应用中打开<br>
                                    选择合适的方式查看文档
                                </div>
                                <div style="margin-top: 20px; display: flex; flex-direction: column; gap: 10px; padding: 0 20px;">
                                    <button onclick="openDocumentOnline('${path}', '${name}')"
                                            style="padding: 12px 20px; background: #0078d4; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        🌐 在线查看器打开
                                    </button>
                                    <button onclick="openInWeChatBrowser('${path}', '${name}', 'doc')"
                                            style="padding: 12px 20px; background: #07c160; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        📱 微信内置浏览器打开
                                    </button>
                                    <button onclick="downloadResource('${path}', '${name}')"
                                            style="padding: 12px 20px; background: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        📥 下载到本地
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                case 'ppt':
                case 'pptx':
                    return `
                        <div class="resource-player">
                            <div class="ppt-viewer">
                                <div class="placeholder-icon">📊</div>
                                <div style="margin-top: 15px; font-size: 16px; color: white; font-weight: 600;">
                                    ${name}
                                </div>
                                <div style="margin-top: 10px; font-size: 14px; color: rgba(255,255,255,0.8); line-height: 1.5; text-align: center; padding: 0 20px;">
                                    PowerPoint演示文稿需要在外部应用中打开<br>
                                    选择合适的方式查看演示文稿
                                </div>
                                <div style="margin-top: 20px; display: flex; flex-direction: column; gap: 10px; padding: 0 20px;">
                                    <button onclick="openPresentationOnline('${path}', '${name}')"
                                            style="padding: 12px 20px; background: #d04423; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        🌐 在线查看器打开
                                    </button>
                                    <button onclick="openInWeChatBrowser('${path}', '${name}', 'ppt')"
                                            style="padding: 12px 20px; background: #07c160; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        📱 微信内置浏览器打开
                                    </button>
                                    <button onclick="downloadResource('${path}', '${name}')"
                                            style="padding: 12px 20px; background: rgba(255,255,255,0.2); color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500;">
                                        📥 下载到本地
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                case 'mp4':
                case 'avi':
                case 'mov':
                case 'webm':
                    return `
                        <div class="resource-player">
                            <div class="video-container" id="videoContainer">
                                <video class="video-player" id="videoPlayer"
                                       preload="auto"
                                       autoplay
                                       muted
                                       playsinline
                                       style="width: 100%; height: 100%; object-fit: contain; background: #000;"
                                       onloadstart="console.log('视频开始加载')"
                                       oncanplay="console.log('视频可以播放'); autoPlayVideo(this)"
                                       onloadeddata="autoPlayVideo(this)"
                                       onerror="handleVideoError(this, '${path}', '${name}')"
                                       ontimeupdate="updateVideoProgress(this)"
                                       onplay="hideVideoOverlay()"
                                       onpause="showVideoOverlay()"
                                       onended="showVideoOverlay()">
                                    <source src="${path}" type="video/${type}">
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #000; color: white; flex-direction: column;">
                                        <div style="font-size: 24px; margin-bottom: 10px;">🎥</div>
                                        <div>您的浏览器不支持视频播放</div>
                                        <button onclick="downloadResource('${path}', '${name}')"
                                                style="margin-top: 15px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                            📥 下载视频
                                        </button>
                                    </div>
                                </video>

                                <!-- 视频覆盖层和中央播放按钮 -->
                                <div class="video-overlay" id="videoOverlay">
                                    <button class="center-play-btn" onclick="toggleVideoPlay()">
                                        <span id="centerPlayIcon">▶</span>
                                    </button>
                                </div>

                                <!-- 视频控制条 -->
                                <div class="video-controls">
                                    <button class="play-btn" onclick="toggleVideoPlay()">
                                        <span id="playIcon">▶</span>
                                    </button>
                                    <div class="progress-bar" onclick="seekVideo(event)">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="time-display">
                                        <span id="currentTime">0:00</span> / <span id="duration">0:00</span>
                                    </div>
                                    <button class="fullscreen-btn" onclick="toggleFullscreen()">
                                        <span id="fullscreenIcon">⛶</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                case 'mp3':
                case 'wav':
                case 'ogg':
                case 'm4a':
                    return `
                        <div class="resource-player">
                            <div class="audio-container" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; flex-direction: column; align-items: center; justify-content: center; color: white; padding: 20px;">
                                <div style="font-size: 48px; margin-bottom: 20px;">🎵</div>
                                <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px; text-align: center;">${name}</div>
                                <div style="font-size: 14px; opacity: 0.8; margin-bottom: 30px;">音频文件</div>

                                <audio id="audioPlayer" controls preload="auto" style="width: 100%; max-width: 300px; margin-bottom: 20px;"
                                       onloadstart="console.log('音频开始加载')"
                                       oncanplay="console.log('音频可以播放')"
                                       onerror="handleAudioError(this, '${path}', '${name}')">
                                    <source src="${path}" type="audio/${type}">
                                    <div style="text-align: center; color: white;">
                                        您的浏览器不支持音频播放
                                    </div>
                                </audio>

                                <div style="display: flex; gap: 10px; flex-wrap: wrap; justify-content: center;">
                                    <button onclick="toggleAudioPlay()" id="audioPlayBtn"
                                            style="padding: 10px 20px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 25px; cursor: pointer; font-size: 14px; backdrop-filter: blur(10px);">
                                        ▶ 播放
                                    </button>
                                    <button onclick="downloadResource('${path}', '${name}')"
                                            style="padding: 10px 20px; background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); border-radius: 25px; cursor: pointer; font-size: 14px; backdrop-filter: blur(10px);">
                                        📥 下载
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                default:
                    return `
                        <div class="resource-player">
                            <div class="placeholder-content">
                                <div class="placeholder-icon">📄</div>
                                <div class="placeholder-text">${name}</div>
                                <div class="placeholder-subtitle">${type.toUpperCase()}文件</div>
                                <button onclick="downloadResource('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    📥 下载文件
                                </button>
                            </div>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">${type.toUpperCase()}文件 • 点击下载</div>
                            </div>
                        </div>
                    `;
            }
        }

        // 打开PDF全屏查看
        function openFullPDF(pdfUrl, title) {
            window.location.href = `pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(title)}`;
        }

        // 自动播放视频函数
        function autoPlayVideo(videoElement) {
            if (!videoElement) return;

            console.log('尝试自动播放视频');

            // 确保视频已加载足够的数据
            if (videoElement.readyState >= 2) {
                videoElement.play().then(() => {
                    console.log('视频自动播放成功');
                    const playIcon = document.getElementById('playIcon');
                    const centerPlayIcon = document.getElementById('centerPlayIcon');
                    if (playIcon) playIcon.textContent = '⏸';
                    if (centerPlayIcon) centerPlayIcon.textContent = '⏸';
                    hideVideoOverlay();

                    // 显示自动播放提示
                    showToast('🎥 视频已自动播放', 'success');
                }).catch(e => {
                    console.log('自动播放失败，可能需要用户交互:', e);
                    // 自动播放失败时显示播放按钮
                    showVideoOverlay();
                    showToast('点击播放按钮开始观看', 'info');
                });
            } else {
                // 如果数据还没加载完，等待一下再试
                setTimeout(() => autoPlayVideo(videoElement), 500);
            }
        }

        // 视频播放控制函数
        function toggleVideoPlay() {
            const video = document.getElementById('videoPlayer');
            const playIcon = document.getElementById('playIcon');
            const centerPlayIcon = document.getElementById('centerPlayIcon');

            if (video) {
                if (video.paused) {
                    video.play().then(() => {
                        if (playIcon) playIcon.textContent = '⏸';
                        if (centerPlayIcon) centerPlayIcon.textContent = '⏸';
                        hideVideoOverlay();
                    }).catch(e => {
                        console.log('播放失败:', e);
                        showToast('播放失败，请检查视频文件', 'error');
                    });
                } else {
                    video.pause();
                    if (playIcon) playIcon.textContent = '▶';
                    if (centerPlayIcon) centerPlayIcon.textContent = '▶';
                    showVideoOverlay();
                }
            }
        }

        function hideVideoOverlay() {
            const overlay = document.getElementById('videoOverlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }
        }

        function showVideoOverlay() {
            const overlay = document.getElementById('videoOverlay');
            if (overlay) {
                overlay.classList.remove('hidden');
            }
        }

        function updateVideoProgress(video) {
            const progressFill = document.getElementById('progressFill');
            const currentTimeSpan = document.getElementById('currentTime');
            const durationSpan = document.getElementById('duration');

            if (progressFill && video.duration) {
                const progress = (video.currentTime / video.duration) * 100;
                progressFill.style.width = progress + '%';
            }

            if (currentTimeSpan) {
                currentTimeSpan.textContent = formatVideoTime(video.currentTime);
            }

            if (durationSpan && video.duration) {
                durationSpan.textContent = formatVideoTime(video.duration);
            }
        }

        function seekVideo(event) {
            const video = document.getElementById('videoPlayer');
            const progressBar = event.currentTarget;

            if (video && video.duration) {
                const rect = progressBar.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const percentage = clickX / rect.width;
                video.currentTime = percentage * video.duration;
            }
        }

        function formatVideoTime(seconds) {
            if (isNaN(seconds)) return '0:00';

            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        // 全屏功能
        function toggleFullscreen() {
            const videoContainer = document.getElementById('videoContainer');
            const fullscreenIcon = document.getElementById('fullscreenIcon');

            if (!videoContainer) return;

            if (!document.fullscreenElement) {
                // 进入全屏
                if (videoContainer.requestFullscreen) {
                    videoContainer.requestFullscreen();
                } else if (videoContainer.webkitRequestFullscreen) {
                    videoContainer.webkitRequestFullscreen();
                } else if (videoContainer.msRequestFullscreen) {
                    videoContainer.msRequestFullscreen();
                }
                if (fullscreenIcon) fullscreenIcon.textContent = '⛶';
            } else {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                if (fullscreenIcon) fullscreenIcon.textContent = '⛶';
            }
        }

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        function handleFullscreenChange() {
            const fullscreenIcon = document.getElementById('fullscreenIcon');
            if (fullscreenIcon) {
                if (document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
                    fullscreenIcon.textContent = '⛶';
                } else {
                    fullscreenIcon.textContent = '⛶';
                }
            }
        }

        // 视频错误处理
        function handleVideoError(videoElement, videoUrl, videoName) {
            console.error('视频加载失败:', videoUrl);
            videoElement.style.display = 'none';

            const errorHtml = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; color: #666; flex-direction: column; padding: 20px;">
                    <div style="font-size: 48px; margin-bottom: 15px;">🎥</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">视频无法播放</div>
                    <div style="font-size: 14px; margin-bottom: 20px; text-align: center;">
                        视频格式不支持或文件损坏<br>
                        您可以尝试下载到本地播放
                    </div>
                    <button onclick="downloadResource('${videoUrl}', '${videoName}')"
                            style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">
                        📥 下载视频
                    </button>
                </div>
            `;

            videoElement.parentElement.innerHTML = errorHtml;
        }

        // 在线打开Word文档
        function openDocumentOnline(docUrl, title) {
            console.log('尝试在线打开文档:', docUrl);

            // 尝试多种在线查看方式
            const viewers = [
                `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(docUrl)}`,
                `https://docs.google.com/viewer?url=${encodeURIComponent(docUrl)}&embedded=true`
            ];

            // 显示加载提示
            showToast('正在打开在线查看器...', 'info');

            // 首先尝试Office Online
            try {
                const newWindow = window.open(viewers[0], '_blank');

                // 检查是否成功打开
                setTimeout(() => {
                    if (!newWindow || newWindow.closed || newWindow.location.href === 'about:blank') {
                        console.log('Office Online打开失败，尝试备选方案');
                        showMobileConfirm(
                            '在线查看器',
                            'Office Online无法打开，是否尝试Google Docs查看器？',
                            () => {
                                window.open(viewers[1], '_blank');
                            },
                            () => {
                                showToast('您可以选择下载文件到本地查看', 'info');
                            }
                        );
                    } else {
                        showToast('文档已在新窗口打开', 'success');
                    }
                }, 2000);
            } catch (e) {
                console.error('打开在线查看器失败:', e);
                showToast('无法打开在线查看器，请尝试下载文件', 'error');
            }
        }

        // 在线打开PowerPoint演示文稿
        function openPresentationOnline(pptUrl, title) {
            console.log('尝试在线打开演示文稿:', pptUrl);

            // 尝试多种在线查看方式
            const viewers = [
                `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(pptUrl)}`,
                `https://docs.google.com/viewer?url=${encodeURIComponent(pptUrl)}&embedded=true`
            ];

            // 显示加载提示
            showToast('正在打开在线查看器...', 'info');

            // 首先尝试Office Online
            try {
                const newWindow = window.open(viewers[0], '_blank');

                // 检查是否成功打开
                setTimeout(() => {
                    if (!newWindow || newWindow.closed || newWindow.location.href === 'about:blank') {
                        console.log('Office Online打开失败，尝试备选方案');
                        showMobileConfirm(
                            '在线查看器',
                            'Office Online无法打开，是否尝试Google Docs查看器？',
                            () => {
                                window.open(viewers[1], '_blank');
                            },
                            () => {
                                showToast('您可以选择下载文件到本地查看', 'info');
                            }
                        );
                    } else {
                        showToast('演示文稿已在新窗口打开', 'success');
                    }
                }, 2000);
            } catch (e) {
                console.error('打开在线查看器失败:', e);
                showToast('无法打开在线查看器，请尝试下载文件', 'error');
            }
        }

        // 在微信浏览器中打开文件
        function openInWeChatBrowser(fileUrl, fileName, fileType) {
            console.log('尝试在微信浏览器中打开文件:', fileUrl);

            // 检测是否在微信环境中
            const isWeChat = /MicroMessenger/i.test(navigator.userAgent);

            if (isWeChat) {
                // 在微信中，直接打开URL
                showToast('正在微信中打开文件...', 'info');
                window.location.href = fileUrl;
            } else {
                // 不在微信中，提供选择
                showMobileConfirm(
                    '打开方式',
                    '当前不在微信环境中，选择打开方式：',
                    () => {
                        // 在当前浏览器中打开
                        window.open(fileUrl, '_blank');
                    },
                    () => {
                        // 复制链接
                        copyToClipboard(fileUrl, fileName);
                    },
                    '浏览器打开',
                    '复制链接'
                );
            }
        }

        // 复制到剪贴板
        function copyToClipboard(text, fileName) {
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showToast(`${fileName} 链接已复制到剪贴板`, 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyTextToClipboard(text, fileName);
                });
            } else {
                fallbackCopyTextToClipboard(text, fileName);
            }
        }

        // 备用复制方法
        function fallbackCopyTextToClipboard(text, fileName) {
            const textArea = document.createElement("textarea");
            textArea.value = text;
            textArea.style.position = "fixed";
            textArea.style.left = "-999999px";
            textArea.style.top = "-999999px";
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                const successful = document.execCommand('copy');
                if (successful) {
                    showToast(`${fileName} 链接已复制到剪贴板`, 'success');
                } else {
                    showToast('复制失败，请手动复制链接', 'error');
                }
            } catch (err) {
                console.error('复制失败:', err);
                showToast('复制失败，请手动复制链接', 'error');
            }

            document.body.removeChild(textArea);
        }

        // 音频播放控制
        function toggleAudioPlay() {
            const audio = document.getElementById('audioPlayer');
            const playBtn = document.getElementById('audioPlayBtn');

            if (audio && playBtn) {
                if (audio.paused) {
                    audio.play().then(() => {
                        playBtn.textContent = '⏸ 暂停';
                        showToast('🎵 音频开始播放', 'success');
                    }).catch(e => {
                        console.log('音频播放失败:', e);
                        showToast('音频播放失败，请检查文件', 'error');
                    });
                } else {
                    audio.pause();
                    playBtn.textContent = '▶ 播放';
                }
            }
        }

        // 音频错误处理
        function handleAudioError(audioElement, audioUrl, audioName) {
            console.error('音频加载失败:', audioUrl);

            const errorHtml = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; flex-direction: column; padding: 20px;">
                    <div style="font-size: 48px; margin-bottom: 15px;">🎵</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">音频无法播放</div>
                    <div style="font-size: 14px; margin-bottom: 20px; text-align: center;">
                        音频格式不支持或文件损坏<br>
                        您可以尝试下载到本地播放
                    </div>
                    <button onclick="downloadResource('${audioUrl}', '${audioName}')"
                            style="background: rgba(255,255,255,0.2); color: white; border: 1px solid rgba(255,255,255,0.3); padding: 12px 24px; border-radius: 25px; font-size: 16px; cursor: pointer; backdrop-filter: blur(10px);">
                        📥 下载音频
                    </button>
                </div>
            `;

            audioElement.parentElement.innerHTML = errorHtml;
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 移动端友好的确认对话框
        function showMobileConfirm(title, message, onConfirm, onCancel, confirmText = '确认返回', cancelText = '继续学习') {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: white;
                border-radius: 12px;
                padding: 24px;
                max-width: 320px;
                width: 100%;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                transform: scale(0.9);
                transition: transform 0.3s ease;
            `;

            dialog.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 8px;">${title}</div>
                    <div style="font-size: 14px; color: #666; line-height: 1.5;">${message}</div>
                </div>
                <div style="display: flex; gap: 12px;">
                    <button id="cancelBtn" style="flex: 1; padding: 12px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; color: #666; font-size: 16px; cursor: pointer;">
                        ${cancelText}
                    </button>
                    <button id="confirmBtn" style="flex: 1; padding: 12px; background: #c00714; border: none; border-radius: 8px; color: white; font-size: 16px; cursor: pointer;">
                        ${confirmText}
                    </button>
                </div>
            `;

            modal.appendChild(dialog);
            document.body.appendChild(modal);

            // 显示动画
            setTimeout(() => {
                modal.style.opacity = '1';
                dialog.style.transform = 'scale(1)';
            }, 50);

            // 绑定事件
            const confirmBtn = dialog.querySelector('#confirmBtn');
            const cancelBtn = dialog.querySelector('#cancelBtn');

            confirmBtn.onclick = () => {
                closeModal();
                if (onConfirm) onConfirm();
            };

            cancelBtn.onclick = () => {
                closeModal();
                if (onCancel) onCancel();
            };

            // 点击背景关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    closeModal();
                    if (onCancel) onCancel();
                }
            };

            function closeModal() {
                modal.style.opacity = '0';
                dialog.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                }, 300);
            }
        }

        // 下载资源
        function downloadResource(resourceUrl, fileName) {
            const link = document.createElement('a');
            link.href = resourceUrl;
            link.download = fileName;
            link.click();
        }

        // 自动启动学习计时
        function autoStartStudyTimer() {
            if (studyStartTime === null) {
                const btn = document.getElementById('studyTimerBtn');
                if (btn) {
                    // 自动开始计时
                    studyStartTime = new Date();
                    btn.innerHTML = '⏱ 已学习 0:00...';
                    btn.classList.add('btn-primary');
                    btn.classList.remove('btn-secondary');

                    // 更新计时显示
                    studyTimer = setInterval(updateStudyTime, 1000);

                    console.log('自动开始学习计时:', studyStartTime);

                    // 显示提示消息
                    showToast('📚 开始学习计时', 'success');

                    // 保持按钮可点击，但改变其功能为显示详细信息
                    btn.onclick = () => {
                        const currentTime = Math.floor((new Date() - studyStartTime) / 1000);
                        showToast(`本次学习时长：${formatStudyTime(currentTime)}`, 'info');
                    };
                }
            }
        }

        // 学习计时功能
        function toggleStudyTimer() {
            const btn = document.getElementById('studyTimerBtn');

            if (studyStartTime === null) {
                // 开始计时
                studyStartTime = new Date();
                btn.innerHTML = '⏱ 学习中...';
                btn.classList.add('btn-primary');
                btn.classList.remove('btn-secondary');

                // 更新计时显示
                studyTimer = setInterval(updateStudyTime, 1000);

                console.log('手动开始学习计时:', studyStartTime);
            } else {
                // 停止计时
                stopStudyTimer();
            }
        }

        function stopStudyTimer() {
            if (studyStartTime !== null) {
                const endTime = new Date();
                const sessionTime = Math.floor((endTime - studyStartTime) / 1000);
                totalStudyTime += sessionTime;

                const btn = document.getElementById('studyTimerBtn');
                btn.innerHTML = `⏱ 已学习 ${formatStudyTime(totalStudyTime)}`;
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');

                if (studyTimer) {
                    clearInterval(studyTimer);
                    studyTimer = null;
                }

                studyStartTime = null;

                console.log('停止学习计时，本次学习:', sessionTime, '秒，总计:', totalStudyTime, '秒');

                // 保存学习记录
                saveStudyRecord(sessionTime);
            }
        }

        function updateStudyTime() {
            if (studyStartTime !== null) {
                const now = new Date();
                const currentSessionTime = Math.floor((now - studyStartTime) / 1000);
                const displayTime = totalStudyTime + currentSessionTime;

                const btn = document.getElementById('studyTimerBtn');
                // 添加动态效果，显示正在计时
                const dots = '.'.repeat((Math.floor(currentSessionTime) % 3) + 1);
                btn.innerHTML = `⏱ 已学习 ${formatStudyTime(displayTime)}${dots}`;

                // 添加轻微的颜色变化表示活跃状态
                btn.style.background = currentSessionTime % 2 === 0 ? '#c00714' : '#a00610';
            }
        }

        function formatStudyTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        // 保存学习记录到服务器
        function saveStudyRecord(sessionTime) {
            if (!currentCourseId || sessionTime < 5) {
                console.log('学习时间太短或缺少课程ID，不记录');
                return;
            }

            const recordData = {
                courseId: currentCourseId,
                studyTime: sessionTime,
                studyDate: new Date().toISOString().split('T')[0],
                deviceType: 'mobile',
                timestamp: Date.now()
            };

            // 异步保存学习记录
            $.ajax({
                url: baseurl + "/study/record/add",
                type: 'POST',
                data: JSON.stringify(recordData),
                headers: {
                    "Authorization": sessionStorage.getItem("header"),
                    "Content-Type": "application/json"
                },
                dataType: 'json',
                success: function(res) {
                    if (res.code == '200') {
                        console.log('学习记录保存成功:', recordData);
                        updateLocalStudyStats(currentCourseId, sessionTime);
                        showToast('学习记录已保存', 'success');
                    } else {
                        console.warn('学习记录保存失败:', res.message);
                        saveToLocalStorage(recordData);
                    }
                },
                error: function(err) {
                    console.error('学习记录保存请求失败:', err);
                    saveToLocalStorage(recordData);
                }
            });
        }

        // 保存学习记录（页面卸载时调用）
        function saveLearningRecord() {
            if (studyStartTime !== null) {
                const sessionTime = Math.floor((Date.now() - studyStartTime) / 1000);
                if (sessionTime >= 5) {
                    saveStudyRecord(sessionTime);
                }
            }
        }

        // 更新本地学习统计
        function updateLocalStudyStats(courseId, studyTime) {
            try {
                const stats = JSON.parse(localStorage.getItem('courseStudyStats') || '{}');
                if (!stats[courseId]) {
                    stats[courseId] = {
                        totalTime: 0,
                        sessionCount: 0,
                        lastStudyDate: null
                    };
                }
                
                stats[courseId].totalTime += studyTime;
                stats[courseId].sessionCount += 1;
                stats[courseId].lastStudyDate = new Date().toISOString();
                
                localStorage.setItem('courseStudyStats', JSON.stringify(stats));
            } catch (e) {
                console.warn('更新本地学习统计失败:', e);
            }
        }

        // 保存到本地存储（离线备份）
        function saveToLocalStorage(recordData) {
            try {
                const offlineRecords = JSON.parse(localStorage.getItem('offlineStudyRecords') || '[]');
                offlineRecords.push(recordData);
                
                // 只保留最近50条记录
                if (offlineRecords.length > 50) {
                    offlineRecords.splice(0, offlineRecords.length - 50);
                }
                
                localStorage.setItem('offlineStudyRecords', JSON.stringify(offlineRecords));
                console.log('学习记录已保存到本地');
                showToast('学习记录已离线保存', 'info');
            } catch (e) {
                console.warn('本地保存失败:', e);
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 创建一个简单的提示
            const toast = document.createElement('div');
            const bgColor = type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3';
            
            toast.style.cssText = `
                position: fixed;
                top: 80px;
                left: 50%;
                transform: translateX(-50%);
                background: ${bgColor};
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                opacity: 0;
                transition: all 0.3s ease;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(-50%) translateY(-10px)';
            }, 100);
            
            // 隐藏动画
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(-50%) translateY(-20px)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }



        // 评分功能
        function showRatingDialog() {
            const ratingHtml = `
                <div id="ratingModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; margin: 20px; max-width: 400px; width: 90%;">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">课程评分</h3>
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 14px; color: #666; margin-bottom: 15px;">请为这门课程打分</div>
                            <div id="starRating" style="font-size: 32px; margin-bottom: 15px;">
                                <span class="star" data-rating="1">⭐</span>
                                <span class="star" data-rating="2">⭐</span>
                                <span class="star" data-rating="3">⭐</span>
                                <span class="star" data-rating="4">⭐</span>
                                <span class="star" data-rating="5">⭐</span>
                            </div>
                            <div id="ratingText" style="font-size: 14px; color: #999; margin-bottom: 20px;">点击星星进行评分</div>
                        </div>
                        <textarea id="ratingComment" placeholder="请输入您的评价（可选）" style="width: 100%; height: 80px; border: 1px solid #ddd; border-radius: 6px; padding: 10px; resize: none; font-size: 14px; margin-bottom: 20px;"></textarea>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="closeRatingDialog()" style="flex: 1; padding: 12px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 6px; cursor: pointer;">取消</button>
                            <button onclick="submitRating()" style="flex: 1; padding: 12px; background: #c00714; color: white; border: none; border-radius: 6px; cursor: pointer;">提交评分</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', ratingHtml);

            // 添加星星点击事件
            const stars = document.querySelectorAll('.star');
            let selectedRating = 0;

            stars.forEach(star => {
                star.style.cursor = 'pointer';
                star.style.opacity = '0.3';

                star.addEventListener('click', function() {
                    selectedRating = parseInt(this.getAttribute('data-rating'));
                    updateStarDisplay(selectedRating);
                    updateRatingText(selectedRating);
                });

                star.addEventListener('mouseover', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    updateStarDisplay(rating);
                });
            });

            document.getElementById('starRating').addEventListener('mouseleave', function() {
                updateStarDisplay(selectedRating);
            });

            function updateStarDisplay(rating) {
                stars.forEach((star, index) => {
                    star.style.opacity = index < rating ? '1' : '0.3';
                });
            }

            function updateRatingText(rating) {
                const texts = ['', '很差', '较差', '一般', '良好', '优秀'];
                document.getElementById('ratingText').textContent = texts[rating] || '点击星星进行评分';
            }

            // 保存选中的评分到全局变量
            window.selectedCourseRating = selectedRating;
        }

        function closeRatingDialog() {
            const modal = document.getElementById('ratingModal');
            if (modal) {
                modal.remove();
            }
        }

        function submitRating() {
            const rating = window.selectedCourseRating || 0;
            const comment = document.getElementById('ratingComment').value.trim();

            if (rating === 0) {
                alert('请先选择评分');
                return;
            }

            // 保存评分
            const ratingData = {
                courseId: currentCourseId,
                rating: rating,
                comment: comment,
                timestamp: new Date().toISOString()
            };

            // 保存到本地存储
            const ratings = JSON.parse(localStorage.getItem('courseRatings') || '[]');
            ratings.push(ratingData);
            localStorage.setItem('courseRatings', JSON.stringify(ratings));

            console.log('课程评分已保存:', ratingData);

            // 这里可以添加发送到服务器的代码
            // submitRatingToServer(ratingData);

            closeRatingDialog();
            alert('评分提交成功！感谢您的反馈。');
        }

        // 访问路径记录功能（参考PC端）
        let learningStartTime = Date.now();
        let isLearningRecordSaved = false;

        // 创建学习记录数据
        function createLearningRecord() {
            if (!currentCourseId) return null;

            const userinfo = sessionStorage.getItem("userinfo");
            if (!userinfo) {
                console.log('用户未登录，无法创建学习记录');
                return null;
            }

            const user = JSON.parse(userinfo);
            const endTime = Date.now();
            const studyDuration = Math.floor((endTime - learningStartTime) / 1000); // 转换为秒

            // 构建学习记录数据
            return {
                courseId: currentCourseId,
                userId: user.id || user.userId,
                userName: user.name || user.username,
                studyTime: studyDuration,
                startTime: new Date(learningStartTime).toISOString(),
                endTime: new Date(endTime).toISOString(),
                pageType: 'course-detail-mobile',
                deviceType: 'mobile',
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
        }

        function saveLearningRecord() {
            if (isLearningRecordSaved || !currentCourseId) return;

            const recordData = createLearningRecord();
            if (!recordData) return;

            console.log('准备保存学习记录:', recordData);

            // 保存到localStorage作为备份
            const existingRecords = JSON.parse(localStorage.getItem("jilu") || "[]");
            existingRecords.push(recordData);
            localStorage.setItem("jilu", JSON.stringify(existingRecords));

            // 尝试发送到服务器 - 使用异步请求避免阻塞
            const authHeader = sessionStorage.getItem("header");
            if (authHeader) {
                // 使用fetch API进行异步提交
                fetch(baseurl + "/api/study/record", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authHeader
                    },
                    body: JSON.stringify(recordData)
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    console.log('学习记录保存成功:', data);
                    isLearningRecordSaved = true;
                    // 清除已成功保存的记录
                    localStorage.removeItem("jilu");
                })
                .catch(error => {
                    console.error('学习记录保存失败:', error);
                    // 尝试备用API
                    tryBackupAPI(recordData, authHeader);
                });
            } else {
                console.log('未找到认证头，仅保存到本地存储');
            }
        }

        // 尝试备用API
        function tryBackupAPI(recordData, authHeader) {
            console.log('尝试备用API提交学习记录');

            // 尝试使用PC端相同的API
            fetch(baseurl + "/study/record/add", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader
                },
                body: JSON.stringify(recordData)
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`备用API也失败: HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('备用API保存成功:', data);
                isLearningRecordSaved = true;
                localStorage.removeItem("jilu");
            })
            .catch(error => {
                console.error('备用API也失败:', error);
                console.log('学习记录已保存到本地存储，等待下次重试');
            });
        }

        // 页面关闭前的处理（参考PC端）
        window.addEventListener('beforeunload', function(e) {
            // 停止学习计时
            if (studyStartTime !== null) {
                stopStudyTimer();
            }

            // 强制保存学习记录
            if (!isLearningRecordSaved) {
                saveLearningRecord();
            }

            // 不显示确认对话框，直接保存
            console.log('页面即将关闭，学习记录已保存');
        });

        // 页面隐藏时也保存记录
        window.addEventListener('pagehide', function(e) {
            if (!isLearningRecordSaved) {
                saveLearningRecord();
            }
        });

        // 页面可见性改变时的处理（参考PC端）
        let isPageInitialized = false;

        document.addEventListener('visibilitychange', function() {
            // 只有在页面初始化完成后才处理可见性变化
            if (!isPageInitialized) return;

            if (document.visibilityState === 'hidden') {
                // 页面隐藏时只保存记录，不停止计时
                console.log('页面隐藏，保存学习记录');
                saveLearningRecord();
            } else if (document.visibilityState === 'visible') {
                // 页面重新可见时重置学习记录状态
                console.log('页面重新可见，重置学习记录状态');
                learningStartTime = Date.now();
                isLearningRecordSaved = false;
            }
        });

        // 定期保存学习记录（每5分钟）- 但不重置计时
        setInterval(function() {
            if (!isLearningRecordSaved && studyStartTime !== null) {
                console.log('定期保存学习记录');
                // 创建一个临时的学习记录，但不标记为已保存
                const tempRecord = createLearningRecord();
                if (tempRecord) {
                    // 保存到本地存储作为备份
                    const existingRecords = JSON.parse(localStorage.getItem("jilu") || "[]");
                    existingRecords.push(tempRecord);
                    localStorage.setItem("jilu", JSON.stringify(existingRecords));
                    console.log('定期备份已保存到本地存储');
                }
            }
        }, 5 * 60 * 1000); // 5分钟

        // 渲染课程资源
        function renderCourseResources(course) {
            let resourcesHtml = '';

            // 检查是否有课程资源列表
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                // 从第二个资源开始显示（第一个已在播放器中显示）
                const remainingResources = course.cmsResourcesCourseMetaList.slice(1);

                if (remainingResources.length > 0) {
                    remainingResources.forEach((resource, index) => {
                        const resourceType = getResourceType(resource.attachPath);
                        const resourceIcon = getResourceIcon(resourceType);

                        resourcesHtml += `
                            <div class="chapter-item">
                                <div class="chapter-number">${resourceIcon}</div>
                                <div class="chapter-info">
                                    <div class="chapter-title">${resource.attachName || `资源 ${index + 2}`}</div>
                                    <div class="chapter-duration">📄 ${resourceType.toUpperCase()}文件</div>
                                </div>
                                <div class="chapter-actions">
                                    <button onclick="openResource('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #c00714; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; margin-right: 8px; cursor: pointer;">
                                        📺 播放
                                    </button>
                                    <button onclick="openResourceInNewWindow('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                                        🔗 打开
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    resourcesHtml = `
                        <div style="text-align: center; padding: 20px; color: #999;">
                            <div style="font-size: 24px; margin-bottom: 10px;">📚</div>
                            <div>主要资源已在上方播放器中显示</div>
                        </div>
                    `;
                }
            } else {
                // 默认显示一些示例章节
                resourcesHtml = `
                    <div class="chapter-item">
                        <div class="chapter-number">1</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第一章：课程导论</div>
                            <div class="chapter-duration">⏱ 30分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第二章：核心内容</div>
                            <div class="chapter-duration">⏱ 45分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第三章：实践应用</div>
                            <div class="chapter-duration">⏱ 35分钟</div>
                        </div>
                    </div>
                `;
            }

            return resourcesHtml;
        }

        // 获取资源类型
        function getResourceType(filePath) {
            if (!filePath) return 'unknown';
            const extension = filePath.toLowerCase().split('.').pop();
            return extension || 'unknown';
        }

        // 获取资源图标
        function getResourceIcon(type) {
            const iconMap = {
                'pdf': '📕',
                'doc': '📄',
                'docx': '📄',
                'ppt': '📊',
                'pptx': '📊',
                'xls': '📈',
                'xlsx': '📈',
                'mp4': '🎥',
                'avi': '🎥',
                'mp3': '🎵',
                'wav': '🎵',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️',
                'gif': '🖼️'
            };
            return iconMap[type] || '📄';
        }

        // 打开资源 - 修改为切换播放器内容
        function openResource(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;
            const playerContainer = document.querySelector('.resource-player');

            if (playerContainer) {
                // 显示加载状态
                playerContainer.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; color: #666; flex-direction: column;">
                        <div class="loading-spinner" style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #c00714; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20px;"></div>
                        <div style="font-size: 16px;">正在加载 ${resourceName}...</div>
                    </div>
                `;

                // 延迟更新内容，让用户看到加载状态
                setTimeout(() => {
                    const newPlayerHtml = renderPlayerByType(resourceType, fullUrl, resourceName);
                    playerContainer.outerHTML = newPlayerHtml;

                    // 滚动到播放器顶部
                    setTimeout(() => {
                        document.querySelector('.resource-player').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 100);
                }, 500);
            }
        }

        // 原始的打开资源功能（用于新窗口打开）
        function openResourceInNewWindow(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;

            if (resourceType === 'pdf') {
                // 跳转到PDF查看器
                window.location.href = `pdf-viewer.html?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(resourceName)}`;
            } else {
                // 其他类型文件直接下载或在新窗口打开
                window.open(fullUrl, '_blank');
            }
        }

        // 渲染默认课程内容（当数据无效时）
        function renderDefaultCourseContent() {
            const courseId = getUrlParam('id');
            const html = `
                <!-- 默认资源播放区域 -->
                <div class="resource-player">
                    <div class="placeholder-content">
                        <div class="placeholder-icon">🎓</div>
                        <div class="placeholder-text">课程加载中...</div>
                        <div class="placeholder-subtitle">正在尝试不同的数据格式</div>
                    </div>
                </div>

                <!-- 默认课程基本信息 -->
                <div class="course-info">
                    <div class="course-header">
                        <div class="course-icon">🎓</div>
                        <div class="course-title">课程 ${courseId || '未知ID'}</div>
                        <div class="course-teacher">讲师：数据加载中...</div>
                        <div class="course-rating">
                            <span>⭐⭐⭐⭐⭐</span>
                            <span>4.5</span>
                        </div>
                    </div>
                    <div class="course-content">
                        <div class="course-category">课程学习</div>
                        <div class="course-stats">
                            <span>📅 加载中...</span>
                            <span>👥 -人学习</span>
                        </div>
                    </div>
                </div>

                <!-- 调试信息 -->
                <div class="course-description">
                    <div class="section-title">🔧 调试信息</div>
                    <div class="description-content" style="font-family: monospace; background: #f5f5f5; padding: 15px; border-radius: 6px;">
                        课程ID: ${courseId}<br>
                        数据获取状态: 等待服务器响应<br>
                        请检查浏览器控制台查看详细信息
                    </div>
                </div>
            `;
            
            document.getElementById('contentArea').innerHTML = html;
            document.getElementById('actionButtons').style.display = 'flex';

            // 尝试重新加载数据
            console.log('渲染默认内容完成，尝试重新获取数据...');
            setTimeout(() => {
                if (courseId) {
                    loadCourseDetailWithFallback(courseId);
                }
            }, 2000);
        }

        // 带降级策略的课程详情加载
        function loadCourseDetailWithFallback(courseId) {
            console.log('尝试降级策略加载课程:', courseId);
            
            // 尝试不同的API端点
            const fallbackAPIs = [
                `/course/meta/${courseId}`,
                `/web/course/${courseId}`,
                `/course/${courseId}`,
                `/courses/${courseId}`
            ];

            let currentAPIIndex = 0;

            function tryNextAPI() {
                if (currentAPIIndex >= fallbackAPIs.length) {
                    console.error('所有API端点都失败了');
                    showError('无法加载课程数据：所有API端点都无响应');
                    return;
                }

                const apiPath = fallbackAPIs[currentAPIIndex];
                console.log(`尝试API端点 ${currentAPIIndex + 1}/${fallbackAPIs.length}: ${apiPath}`);

                $.ajax({
                    url: baseurl + apiPath,
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json',
                    success: (res) => {
                        console.log(`API端点 ${apiPath} 响应:`, res);
                        
                        let courseData = null;
                        // 尝试多种响应格式
                        if (res && res.code == '200' && res.data) {
                            courseData = res.data;
                        } else if (res && res.code == 200 && res.data) {
                            courseData = res.data;
                        } else if (res && typeof res === 'object' && (res.id || res.title)) {
                            courseData = res;
                        } else if (res && res.success && res.data) {
                            courseData = res.data;
                        } else if (res && Array.isArray(res) && res.length > 0) {
                            courseData = res[0];
                        }
                        
                        if (courseData) {
                            console.log(`API端点 ${apiPath} 成功返回数据`);
                            renderCourseDetail(courseData);
                            recordCourseClick(courseId);
                        } else {
                            console.log(`API端点 ${apiPath} 数据格式不符合预期，尝试下一个`);
                            currentAPIIndex++;
                            tryNextAPI();
                        }
                    },
                    error: (err) => {
                        console.error(`API端点 ${apiPath} 请求失败:`, err);
                        currentAPIIndex++;
                        tryNextAPI();
                    }
                });
            }

            tryNextAPI();
        }

        // 显示错误信息
        function showError(message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">🎓</div>
                    <div class="error-message">${message}</div>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                    <div style="margin-top: 15px; font-size: 12px; color: #666; font-family: monospace;">
                        调试信息：请检查浏览器控制台
                    </div>
                </div>
            `;
            document.getElementById('contentArea').innerHTML = html;
        }
        
        // 添加到收藏
        function addToFavorites() {
            // 这里可以添加收藏功能的实现
            alert('收藏功能开发中...');
        }
        
        // 开始学习
        function startLearning() {
            const courseId = getUrlParam('id');
            if (courseId) {
                // 跳转到学习页面
                window.location.href = `learning.html?id=${courseId}&type=course`;
            } else {
                alert('无法获取课程信息');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const courseId = getUrlParam('id');
            if (courseId) {
                currentCourseId = courseId; // 设置全局课程ID
                loadCourseDetail(courseId);

                // 自动开始学习计时
                setTimeout(() => {
                    autoStartStudyTimer();
                }, 2000); // 延迟2秒开始计时，让用户看到界面

                // 初始化学习记录
                learningStartTime = Date.now();
                isLearningRecordSaved = false;

                // 标记页面初始化完成
                setTimeout(() => {
                    isPageInitialized = true;
                    console.log('页面初始化完成，开始监听可见性变化');
                }, 3000); // 延迟3秒确保所有初始化完成

                console.log('课程详情页面初始化完成，课程ID:', courseId);
            } else {
                showError('缺少课程ID参数');
            }
        });
    </script>
</body>
</html>
