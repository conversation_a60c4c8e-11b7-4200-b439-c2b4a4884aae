<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>心声社区 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 社区页面专用样式 */
        .community-header {
            background: linear-gradient(135deg, #FF6B6B 0%, #FF4757 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .community-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .community-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .community-stats {
            display: flex;
            gap: 24px;
            margin-top: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .community-tabs {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .tab-buttons {
            display: flex;
            background: #f5f5f5;
            border-radius: 8px;
            padding: 4px;
        }
        
        .tab-button {
            flex: 1;
            background: none;
            border: none;
            padding: 10px 16px;
            border-radius: 6px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: white;
            color: #c00714;
            font-weight: 500;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .community-content {
            padding: 16px;
        }
        
        .post-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .post-item:active {
            transform: translateY(1px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .post-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            margin-right: 12px;
        }
        
        .post-info {
            flex: 1;
        }
        
        .post-author {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }
        
        .post-time {
            font-size: 12px;
            color: #999;
        }
        
        .post-badge {
            background: #c00714;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .post-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            line-height: 1.4;
            margin-bottom: 8px;
        }
        
        .post-content {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .post-footer {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 12px;
            border-top: 1px solid #f5f5f5;
        }
        
        .post-actions {
            display: flex;
            gap: 16px;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            background: none;
            border: none;
            color: #999;
            font-size: 12px;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 16px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            background: #f5f5f5;
            color: #666;
        }
        
        .action-btn.liked {
            color: #c00714;
        }
        
        .action-btn svg {
            width: 14px;
            height: 14px;
        }
        
        .post-category {
            background: rgba(192, 7, 20, 0.1);
            color: #c00714;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .load-more {
            text-align: center;
            padding: 20px;
        }
        
        .load-more-btn {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e5e5e5;
            padding: 12px 24px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .load-more-btn:hover {
            background: #e9ecef;
        }
        
        .fab {
            position: fixed;
            bottom: 80px;
            right: 16px;
            width: 56px;
            height: 56px;
            background: #c00714;
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(192, 7, 20, 0.3);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            z-index: 999;
            transition: all 0.3s ease;
        }
        
        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(192, 7, 20, 0.4);
        }
        
        @media (max-width: 375px) {
            .community-header {
                padding: 16px 12px;
            }
            
            .community-content {
                padding: 12px;
            }
            
            .post-item {
                padding: 12px;
                margin-bottom: 8px;
            }
        }
    </style>
</head>
<body class="mobile-community">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">心声社区</h2>
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 社区头部 -->
    <section class="community-header">
        <div class="community-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            心声社区
        </div>
        <div class="community-subtitle">分享思想，传递正能量</div>
        <div class="community-stats">
            <div class="stat-item">
                <div class="stat-number" id="totalPosts">0</div>
                <div class="stat-label">帖子总数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="todayPosts">0</div>
                <div class="stat-label">今日新增</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="activeUsers">0</div>
                <div class="stat-label">活跃用户</div>
            </div>
        </div>
    </section>

    <!-- 标签切换 -->
    <section class="community-tabs">
        <div class="tab-buttons">
            <button class="tab-button active" data-tab="latest">最新</button>
            <button class="tab-button" data-tab="hot">热门</button>
            <button class="tab-button" data-tab="my">我的</button>
        </div>
    </section>

    <!-- 社区内容 -->
    <main class="community-content">
        <div id="postsContainer">
            <!-- 帖子列表将通过JavaScript动态加载 -->
        </div>
        
        <div class="load-more" id="loadMore" style="display: none;">
            <button class="load-more-btn" onclick="loadMorePosts()">加载更多</button>
        </div>
    </main>

    <!-- 发布按钮 -->
    <button class="fab" onclick="createPost()">+</button>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索社区内容..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let currentTab = 'latest';
        let currentPage = 1;
        let isLoading = false;
        let hasMore = true;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            initSearch();
            
            // 检查登录状态
            updateLoginUI();
            
            // 初始化标签切换
            initTabs();
            
            // 加载社区数据
            loadCommunityStats();
            loadPosts();
        });

        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tab = this.getAttribute('data-tab');
                    
                    // 更新标签状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容
                    currentTab = tab;
                    currentPage = 1;
                    hasMore = true;
                    
                    // 重新加载数据
                    loadPosts();
                });
            });
        }

        function loadCommunityStats() {
            // 模拟统计数据
            document.getElementById('totalPosts').textContent = '1,234';
            document.getElementById('todayPosts').textContent = '56';
            document.getElementById('activeUsers').textContent = '789';
        }

        function loadPosts() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('postsContainer');
            
            if (currentPage === 1) {
                container.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';
            }
            
            // 根据当前标签加载不同数据
            let apiParams = {
                pageNum: currentPage,
                pageSize: 10
            };
            
            if (currentTab === 'hot') {
                apiParams.sort = 'clickCount,desc';
            } else if (currentTab === 'my') {
                // 需要登录状态
                const loginStatus = checkLoginStatus();
                if (!loginStatus.isLoggedIn) {
                    container.innerHTML = '<div class="empty-state">请先登录查看我的帖子</div>';
                    isLoading = false;
                    return;
                }
                apiParams.authorId = loginStatus.userInfo.id;
            } else {
                apiParams.sort = 'publishedTime,desc';
            }
            
            // 获取社区帖子数据
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: apiParams,
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderPosts(res.data.list || [], currentPage === 1);

                        // 更新分页状态
                        hasMore = res.data.list && res.data.list.length === apiParams.pageSize;
                        updateLoadMoreButton();
                    } else {
                        if (currentPage === 1) {
                            container.innerHTML = '<div class="empty-state">暂无帖子</div>';
                        }
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载帖子失败:', err);
                    if (currentPage === 1) {
                        container.innerHTML = '<div class="empty-state">加载失败，请稍后重试</div>';
                    }
                    isLoading = false;
                }
            });
        }

        function renderPosts(posts, replace = false) {
            const container = document.getElementById('postsContainer');
            
            if (!posts || posts.length === 0) {
                if (replace) {
                    container.innerHTML = '<div class="empty-state">暂无帖子</div>';
                }
                return;
            }
            
            let html = '';
            posts.forEach(post => {
                html += createPostHTML(post);
            });
            
            if (replace) {
                container.innerHTML = html;
            } else {
                container.innerHTML += html;
            }
            
            // 绑定事件
            bindPostEvents();
        }

        function createPostHTML(post) {
            const authorName = post.authorName || '匿名用户';
            const avatar = authorName.charAt(0).toUpperCase();
            const isNew = MobileUtils.formatTime(post.publishedTime).includes('分钟前');
            
            return `
                <div class="post-item" data-id="${post.id}">
                    <div class="post-header">
                        <div class="post-avatar">${avatar}</div>
                        <div class="post-info">
                            <div class="post-author">${authorName}</div>
                            <div class="post-time">${MobileUtils.formatTime(post.publishedTime)}</div>
                        </div>
                        ${isNew ? '<span class="post-badge">NEW</span>' : ''}
                    </div>
                    <div class="post-title">${post.title || '无标题'}</div>
                    <div class="post-content">${post.content || post.summary || '暂无内容'}</div>
                    <div class="post-footer">
                        <div class="post-actions">
                            <button class="action-btn" onclick="likePost('${post.id}')">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                                ${post.likeCount || 0}
                            </button>
                            <button class="action-btn" onclick="commentPost('${post.id}')">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M21 6h-2v9H6v2c0 .55.45 1 1 1h11l4 4V7c0-.55-.45-1-1-1zm-4 6V3c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v14l4-4h11c.55 0 1-.45 1-1z"/>
                                </svg>
                                ${post.commentCount || 0}
                            </button>
                            <button class="action-btn" onclick="sharePost('${post.id}')">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .24.04.47.09.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
                                </svg>
                                分享
                            </button>
                        </div>
                        <span class="post-category">${post.categoryName || '心声社区'}</span>
                    </div>
                </div>
            `;
        }

        function bindPostEvents() {
            const postItems = document.querySelectorAll('.post-item');
            postItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // 如果点击的是按钮，不触发跳转
                    if (e.target.closest('.action-btn')) return;
                    
                    const postId = this.getAttribute('data-id');
                    if (postId) {
                        clicknum(postId);
                        window.location.href = `community-detail.html?id=${postId}`;
                    }
                });
            });
        }

        function updateLoadMoreButton() {
            const loadMore = document.getElementById('loadMore');
            if (hasMore && currentPage > 1) {
                loadMore.style.display = 'block';
            } else {
                loadMore.style.display = 'none';
            }
        }

        function loadMorePosts() {
            if (!hasMore || isLoading) return;
            
            currentPage++;
            loadPosts();
        }

        function likePost(postId) {
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast('请先登录', 'warning');
                return;
            }
            
            // 这里应该调用点赞API
            MobileUtils.showToast('点赞成功', 'success');
        }

        function commentPost(postId) {
            window.location.href = `community-detail.html?id=${postId}#comments`;
        }

        function sharePost(postId) {
            if (navigator.share) {
                navigator.share({
                    title: '心声社区 - 思政一体化平台',
                    url: window.location.origin + `/Mobile/pages/community-detail.html?id=${postId}`
                });
            } else {
                MobileUtils.showToast('已复制链接到剪贴板', 'success');
            }
        }

        function createPost() {
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast('请先登录', 'warning');
                setTimeout(() => {
                    window.location.href = '../login.html';
                }, 1000);
                return;
            }
            
            window.location.href = 'create-post.html';
        }
    </script>
</body>
</html>
