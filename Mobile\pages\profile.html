<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 个人中心页面专用样式 */
        .profile-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px 16px 24px;
            margin-top: 56px;
            position: relative;
        }

        .profile-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }

        .profile-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 600;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .profile-details {
            flex: 1;
        }

        .profile-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .profile-id {
            font-size: 13px;
            opacity: 0.8;
            margin-bottom: 8px;
        }

        .profile-level {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 11px;
            display: inline-block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 8px;
            text-align: center;
            backdrop-filter: blur(10px);
        }

        .stat-number {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 10px;
            opacity: 0.8;
        }
        
        .profile-content {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 200px);
        }

        .quick-actions {
            margin-bottom: 20px;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            background: white;
            border-radius: 12px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            min-height: 80px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }

        .action-item:active {
            transform: scale(0.95);
            background: #f8f9fa;
        }

        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: white;
        }

        .action-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
            min-width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .action-icon.learning {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .action-icon.achievement {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        .action-icon.tasks {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .action-icon.settings {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }

        .action-icon.help {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .action-icon.logout {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }

        .action-icon.stats {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        }

        .action-icon.exam {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        }

        .action-icon.voice {
            background: linear-gradient(135deg, #ff9a9e 0%, #fad0c4 100%);
        }

        .action-icon svg {
            width: 20px;
            height: 20px;
        }

        .action-item span {
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }
        
        .login-prompt {
            background: white;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .login-prompt-icon {
            width: 80px;
            height: 80px;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: #999;
            font-size: 32px;
        }

        .login-prompt-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .login-prompt-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }

        .login-prompt-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .login-prompt-btn:hover {
            background: #a00610;
        }
        
        @media (max-width: 375px) {
            .profile-header {
                padding: 16px 12px 32px;
            }
            
            .profile-content {
                padding: 12px;
            }
            
            .profile-avatar {
                width: 64px;
                height: 64px;
                font-size: 24px;
            }
            
            .profile-name {
                font-size: 20px;
            }
            
            .stats-grid {
                gap: 12px;
            }
            
            .stat-card {
                padding: 12px;
            }
        }
    </style>
</head>
<body class="mobile-profile">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">个人中心</h2>
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 个人信息头部 -->
    <section class="profile-header" id="profileHeader">
        <!-- 登录状态下显示 -->
        <div class="profile-info" id="profileInfo" style="display: none;">
            <div class="profile-avatar" id="profileAvatar">用</div>
            <div class="profile-details">
                <div class="profile-name" id="profileName">用户名</div>
                <div class="profile-id" id="profileId">学号：202100001</div>
                <div class="profile-level">活跃学员</div>
            </div>
        </div>
        
        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stat-card">
                <div class="stat-number">24</div>
                <div class="stat-label">已学课程</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">学习时长</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">学习积分</div>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <main class="profile-content">
        <!-- 未登录提示 -->
        <div class="login-prompt" id="loginPrompt">
            <div class="login-prompt-icon">👤</div>
            <div class="login-prompt-title">请先登录</div>
            <div class="login-prompt-text">登录后可查看个人学习记录和成就</div>
            <button class="login-prompt-btn" onclick="goToLogin()">立即登录</button>
        </div>

        <!-- 学生端功能 -->
        <div class="quick-actions" id="studentSection" style="display: none;">
            <div class="actions-grid">
                <a href="learning-records.html" class="action-item">
                    <div class="action-icon learning">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/>
                        </svg>
                    </div>
                    <span>学习路径</span>
                </a>

                <a href="student-tasks.html" class="action-item">
                    <div class="action-badge">5</div>
                    <div class="action-icon tasks">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <span>学习任务</span>
                </a>

                <a href="achievements.html" class="action-item">
                    <div class="action-badge">3</div>
                    <div class="action-icon achievement">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                        </svg>
                    </div>
                    <span>考试成绩</span>
                </a>

                <a href="release-voice.html" class="action-item">
                    <div class="action-icon voice">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.81 2 15.5 2.5 16.97 3.42L15.54 4.85C14.5 4.32 13.28 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12H22C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM19 3V7H15V5H17V3H19ZM12 7V13L16.25 15.15L15.25 17.05L10 14V7H12Z"/>
                        </svg>
                    </div>
                    <span>发布心声</span>
                </a>

                <a href="settings.html" class="action-item">
                    <div class="action-icon settings">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.68 19.18,11.36 19.13,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.93,5.93 15.45,5.64 14.92,5.42L14.5,2.81C14.46,2.57 14.25,2.4 14,2.4H10.08C9.83,2.4 9.62,2.57 9.58,2.81L9.16,5.42C8.63,5.64 8.15,5.93 7.7,6.29L5.31,5.33C5.09,5.26 4.84,5.33 4.72,5.55L2.8,8.87C2.68,9.07 2.73,9.34 2.91,9.48L4.94,11.06C4.89,11.36 4.87,11.68 4.87,12C4.87,12.33 4.89,12.64 4.94,12.94L2.91,14.52C2.73,14.66 2.68,14.93 2.8,15.13L4.72,18.45C4.84,18.67 5.09,18.74 5.31,18.67L7.7,17.71C8.15,18.07 8.63,18.36 9.16,18.58L9.58,21.19C9.62,21.43 9.83,21.6 10.08,21.6H14C14.25,21.6 14.46,21.43 14.5,21.19L14.92,18.58C15.45,18.36 15.93,18.07 16.38,17.71L18.77,18.67C18.99,18.74 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.93 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z"/>
                        </svg>
                    </div>
                    <span>系统设置</span>
                </a>

                <a href="help.html" class="action-item">
                    <div class="action-icon help">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M11,18H13V16H11V18ZM12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20ZM12,6C9.79,6 8,7.79 8,10H10C10,8.9 10.9,8 12,8C13.1,8 14,8.9 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10C16,7.79 14.21,6 12,6Z"/>
                        </svg>
                    </div>
                    <span>帮助中心</span>
                </a>

                <a href="#" class="action-item" id="logoutBtn" onclick="performLogout()" style="display: none;">
                    <div class="action-icon logout">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17,7L15.59,8.41L18.17,11H8V13H18.17L15.59,15.59L17,17L22,12L17,7ZM4,5H12V3H4C2.9,3 2,3.9 2,5V19C2,20.1 2.9,21 4,21H12V19H4V5Z"/>
                        </svg>
                    </div>
                    <span>退出登录</span>
                </a>

                <div class="action-item" style="visibility: hidden;"></div>
            </div>
        </div>

        <!-- 教师端功能 -->
        <div class="quick-actions" id="teacherSection" style="display: none;">
            <div class="actions-grid">
                <a href="student-achievement.html" class="action-item">
                    <div class="action-icon stats">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 6L18.29 8.29L13.41 13.17L9.41 9.17L2 16.59L3.41 18L9.41 12L13.41 16L20.71 8.71L23 11V6H16Z"/>
                        </svg>
                    </div>
                    <span>成绩统计</span>
                </a>

                <a href="student-learning.html" class="action-item">
                    <div class="action-icon learning">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                        </svg>
                    </div>
                    <span>学习统计</span>
                </a>

                <a href="exam-management.html" class="action-item">
                    <div class="action-icon exam">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <span>考试管理</span>
                </a>

                <a href="task-management.html" class="action-item">
                    <div class="action-icon tasks">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.68 19.18,11.36 19.13,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.93,5.93 15.45,5.64 14.92,5.42L14.5,2.81C14.46,2.57 14.25,2.4 14,2.4H10.08C9.83,2.4 9.62,2.57 9.58,2.81L9.16,5.42C8.63,5.64 8.15,5.93 7.7,6.29L5.31,5.33C5.09,5.26 4.84,5.33 4.72,5.55L2.8,8.87C2.68,9.07 2.73,9.34 2.91,9.48L4.94,11.06C4.89,11.36 4.87,11.68 4.87,12C4.87,12.33 4.89,12.64 4.94,12.94L2.91,14.52C2.73,14.66 2.68,14.93 2.8,15.13L4.72,18.45C4.84,18.67 5.09,18.74 5.31,18.67L7.7,17.71C8.15,18.07 8.63,18.36 9.16,18.58L9.58,21.19C9.62,21.43 9.83,21.6 10.08,21.6H14C14.25,21.6 14.46,21.43 14.5,21.19L14.92,18.58C15.45,18.36 15.93,18.07 16.38,17.71L18.77,18.67C18.99,18.74 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.93 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z"/>
                        </svg>
                    </div>
                    <span>任务管理</span>
                </a>

                <a href="achievements-showcase.html" class="action-item">
                    <div class="action-icon achievement">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                        </svg>
                    </div>
                    <span>成果展示</span>
                </a>

                <a href="release-voice.html" class="action-item">
                    <div class="action-icon voice">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C13.81 2 15.5 2.5 16.97 3.42L15.54 4.85C14.5 4.32 13.28 4 12 4C7.58 4 4 7.58 4 12C4 16.42 7.58 20 12 20C16.42 20 20 16.42 20 12H22C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2ZM19 3V7H15V5H17V3H19ZM12 7V13L16.25 15.15L15.25 17.05L10 14V7H12Z"/>
                        </svg>
                    </div>
                    <span>发布心声</span>
                </a>

                <a href="settings.html" class="action-item">
                    <div class="action-icon settings">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.68 19.18,11.36 19.13,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.93,5.93 15.45,5.64 14.92,5.42L14.5,2.81C14.46,2.57 14.25,2.4 14,2.4H10.08C9.83,2.4 9.62,2.57 9.58,2.81L9.16,5.42C8.63,5.64 8.15,5.93 7.7,6.29L5.31,5.33C5.09,5.26 4.84,5.33 4.72,5.55L2.8,8.87C2.68,9.07 2.73,9.34 2.91,9.48L4.94,11.06C4.89,11.36 4.87,11.68 4.87,12C4.87,12.33 4.89,12.64 4.94,12.94L2.91,14.52C2.73,14.66 2.68,14.93 2.8,15.13L4.72,18.45C4.84,18.67 5.09,18.74 5.31,18.67L7.7,17.71C8.15,18.07 8.63,18.36 9.16,18.58L9.58,21.19C9.62,21.43 9.83,21.6 10.08,21.6H14C14.25,21.6 14.46,21.43 14.5,21.19L14.92,18.58C15.45,18.36 15.93,18.07 16.38,17.71L18.77,18.67C18.99,18.74 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.93 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z"/>
                        </svg>
                    </div>
                    <span>系统设置</span>
                </a>

                <a href="help.html" class="action-item">
                    <div class="action-icon help">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M11,18H13V16H11V18ZM12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20ZM12,6C9.79,6 8,7.79 8,10H10C10,8.9 10.9,8 12,8C13.1,8 14,8.9 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10C16,7.79 14.21,6 12,6Z"/>
                        </svg>
                    </div>
                    <span>帮助中心</span>
                </a>

                <a href="#" class="action-item" id="logoutBtnTeacher" onclick="performLogout()" style="display: none;">
                    <div class="action-icon logout">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M17,7L15.59,8.41L18.17,11H8V13H18.17L15.59,15.59L17,17L22,12L17,7ZM4,5H12V3H4C2.9,3 2,3.9 2,5V19C2,20.1 2.9,21 4,21H12V19H4V5Z"/>
                        </svg>
                    </div>
                    <span>退出登录</span>
                </a>
            </div>
        </div>


    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            initSearch();
            
            // 检查登录状态并更新UI
            updateProfileUI();
        });

        function updateProfileUI() {
            const loginStatus = checkLoginStatus();
            const loginPrompt = document.getElementById('loginPrompt');
            const profileInfo = document.getElementById('profileInfo');
            const statsGrid = document.getElementById('statsGrid');
            const studentSection = document.getElementById('studentSection');
            const teacherSection = document.getElementById('teacherSection');
            const logoutBtn = document.getElementById('logoutBtn');
            const logoutBtnTeacher = document.getElementById('logoutBtnTeacher');

            if (loginStatus.isLoggedIn) {
                // 已登录状态
                loginPrompt.style.display = 'none';
                profileInfo.style.display = 'flex';
                statsGrid.style.display = 'grid';

                // 更新用户信息
                const userInfo = loginStatus.userInfo;
                const profileAvatar = document.getElementById('profileAvatar');
                const profileName = document.getElementById('profileName');
                const profileId = document.getElementById('profileId');

                if (profileAvatar && userInfo.name) {
                    profileAvatar.textContent = userInfo.name.charAt(0).toUpperCase();
                }

                if (profileName && userInfo.name) {
                    profileName.textContent = userInfo.name;
                }

                // 根据用户角色显示不同的菜单和信息
                if (userInfo.roleName === '老师') {
                    // 教师端
                    // 优先使用userAuth.identifier，如果没有则使用identifier
                    const teacherIdentifier = userInfo.userAuth?.identifier || userInfo.identifier;
                    if (profileId && teacherIdentifier) {
                        profileId.textContent = `教工号：${teacherIdentifier}`;
                    } else if (profileId) {
                        profileId.textContent = '教工号：未设置';
                    }
                    studentSection.style.display = 'none';
                    teacherSection.style.display = 'block';
                    if (logoutBtn) logoutBtn.style.display = 'none';
                    if (logoutBtnTeacher) logoutBtnTeacher.style.display = 'block';

                    // 更新教师统计数据
                    updateTeacherStats();
                } else {
                    // 学生端
                    // 优先使用userAuth.identifier，如果没有则使用identifier
                    const studentIdentifier = userInfo.userAuth?.identifier || userInfo.identifier;
                    if (profileId && studentIdentifier) {
                        profileId.textContent = `学号：${studentIdentifier}`;
                    } else if (profileId) {
                        profileId.textContent = '学号：未设置';
                    }
                    studentSection.style.display = 'block';
                    teacherSection.style.display = 'none';
                    if (logoutBtn) logoutBtn.style.display = 'block';
                    if (logoutBtnTeacher) logoutBtnTeacher.style.display = 'none';

                    // 加载学习统计数据
                    loadLearningStats();

                    // 加载最近学习记录
                    loadRecentLearning();
                }
            } else {
                // 未登录状态
                loginPrompt.style.display = 'block';
                profileInfo.style.display = 'none';
                statsGrid.style.display = 'none';
                studentSection.style.display = 'none';
                teacherSection.style.display = 'none';
                if (logoutBtn) logoutBtn.style.display = 'none';
                if (logoutBtnTeacher) logoutBtnTeacher.style.display = 'none';
            }
        }

        function updateTeacherStats() {
            // 获取教师班级绑定数据来统计管理班级数
            Promise.all([
                // 获取教师班级绑定关系
                $.ajax({
                    url: baseurl + "/binding/teacher-class",
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    dataType: 'json'
                }).catch(() => ({ code: 'error', data: [] })),

                // 获取教师创建的任务数量
                $.ajax({
                    url: baseurl + "/learning-tasks/weblist",
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    data: {
                        pageNum: 1,
                        pageSize: 1000,
                        tasksName: "",
                        sectionId: ""
                    },
                    dataType: 'json'
                }).catch(() => ({ code: 'error', data: { list: [], total: 0 } })),

                // 获取学生总数统计
                $.ajax({
                    url: baseurl + "/student/ClaassStudentlist",
                    type: 'GET',
                    contentType: "application/json",
                    headers: {
                        "Authorization": sessionStorage.getItem("header")
                    },
                    data: {
                        pageNum: 1,
                        pageSize: 1
                    },
                    dataType: 'json'
                }).catch(() => ({ code: 'error', data: { total: 0 } }))
            ]).then(([classRes, taskRes, studentRes]) => {
                let managedClasses = 0;
                let totalStudents = 0;
                let createdTasks = 0;

                // 处理班级绑定数据
                if (classRes.code == '200' && classRes.data) {
                    const classData = classRes.data;
                    // 计算管理的班级总数和学生数
                    classData.forEach(college => {
                        if (college.children) {
                            college.children.forEach(major => {
                                if (major.children) {
                                    managedClasses += major.children.length;
                                    // 每个班级估算35-40人
                                    totalStudents += major.children.length * 38;
                                }
                            });
                        }
                    });

                    // 如果没有绑定数据，使用用户角色信息中的班级数据
                    if (managedClasses === 0) {
                        const userInfo = JSON.parse(sessionStorage.getItem('userinfo') || '{}');
                        if (userInfo.roleMojorList && userInfo.roleMojorList.length > 0) {
                            managedClasses = userInfo.roleMojorList.length;
                            totalStudents = managedClasses * 38; // 估算每班38人
                        }
                    }
                }

                // 处理任务数据
                if (taskRes.code == '200' && taskRes.data) {
                    createdTasks = taskRes.data.total || taskRes.data.list?.length || 0;
                }

                // 处理学生总数数据
                if (studentRes.code == '200' && studentRes.data) {
                    // 如果API返回了准确的学生总数，使用API数据
                    const apiStudentCount = studentRes.data.total || 0;
                    if (apiStudentCount > 0) {
                        totalStudents = apiStudentCount;
                    }
                }

                // 更新显示
                updateTeacherStatsDisplay({
                    managedClasses: managedClasses,
                    createdTasks: createdTasks,
                    totalStudents: totalStudents
                });
            }).catch((err) => {
                console.error('获取教师统计失败:', err);
                // 使用基于用户信息的模拟数据
                const userInfo = JSON.parse(sessionStorage.getItem('userinfo') || '{}');
                let mockClasses = 3; // 默认管理3个班级
                let mockTasks = 8;   // 默认创建8个任务
                let mockStudents = 114; // 默认114个学生

                // 如果用户有角色信息，基于此调整数据
                if (userInfo.roleMojorList && userInfo.roleMojorList.length > 0) {
                    mockClasses = userInfo.roleMojorList.length;
                    mockStudents = mockClasses * 38; // 每班38人
                    mockTasks = Math.max(5, mockClasses * 2); // 每个班级至少2个任务
                }

                updateTeacherStatsDisplay({
                    managedClasses: mockClasses,
                    createdTasks: mockTasks,
                    totalStudents: mockStudents
                });
            });
        }

        function updateTeacherStatsDisplay(stats) {
            // 更新教师统计数据显示
            const statNumbers = document.querySelectorAll('.stat-number');
            const statLabels = document.querySelectorAll('.stat-label');

            if (statNumbers.length >= 3 && statLabels.length >= 3) {
                statNumbers[0].textContent = stats.managedClasses || '0';
                statLabels[0].textContent = '管理班级';

                statNumbers[1].textContent = stats.createdTasks || '0';
                statLabels[1].textContent = '创建任务';

                statNumbers[2].textContent = stats.totalStudents || '0';
                statLabels[2].textContent = '学生总数';
            }
        }

        function goToLogin() {
            window.location.href = '../login.html';
        }

        function performLogout() {
            if (confirm('确定要退出登录吗？')) {
                logout();
                updateProfileUI();
                MobileUtils.showToast('已退出登录', 'success');
            }
        }

        function loadLearningStats() {
            // 获取学习统计数据
            $.ajax({
                url: baseurl + "/study/record/stats",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        updateStatsDisplay(res.data);
                    } else {
                        updateStatsDisplay({});
                    }
                },
                error: (err) => {
                    console.error('获取学习统计失败:', err);
                    updateStatsDisplay({});
                }
            });
        }

        function updateStatsDisplay(stats) {
            // 更新学习天数
            const studyDaysElement = document.querySelector('.stat-number');
            if (studyDaysElement) {
                studyDaysElement.textContent = stats.studyDays || '0';
            }

            // 更新其他统计数据
            const statNumbers = document.querySelectorAll('.stat-number');
            if (statNumbers.length >= 3) {
                statNumbers[0].textContent = stats.studyDays || '0';
                statNumbers[1].textContent = stats.completedCourses || '0';
                statNumbers[2].textContent = stats.studyHours || '0';
            }
        }

        function loadRecentLearning() {
            // 获取最近学习记录
            $.ajax({
                url: baseurl + "/study/record/recent",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 5
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderRecentLearning(res.data.list || []);
                    } else {
                        renderEmptyLearning();
                    }
                },
                error: (err) => {
                    console.error('获取学习记录失败:', err);
                    renderEmptyLearning();
                }
            });
        }

        function renderRecentLearning(records) {
            const container = document.querySelector('.learning-list');
            if (!container) return;

            if (!records || records.length === 0) {
                renderEmptyLearning();
                return;
            }

            let html = '';
            records.forEach(record => {
                html += `
                    <div class="learning-item">
                        <div class="learning-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
                            </svg>
                        </div>
                        <div class="learning-content">
                            <div class="learning-title">${record.title || record.type || '学习内容'}</div>
                            <div class="learning-time">${MobileUtils.formatTime(record.createTime)}</div>
                        </div>
                        <div class="learning-progress">
                            <span class="progress-text">${record.progress || '100%'}</span>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function renderEmptyLearning() {
            const container = document.querySelector('.learning-list');
            if (!container) return;

            container.innerHTML = `
                <div class="empty-state" style="text-align: center; padding: 40px 20px; color: #999;">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 48px; height: 48px; margin-bottom: 16px; opacity: 0.5;">
                        <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
                    </svg>
                    <div>暂无学习记录</div>
                    <div style="font-size: 12px; margin-top: 8px;">开始学习来记录你的进步吧</div>
                </div>
            `;
        }
    </script>
</body>
</html>
