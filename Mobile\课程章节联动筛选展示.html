<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程章节联动筛选功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 400px;
            margin: 20px auto;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        
        .demo-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-title {
            font-size: 20px;
            font-weight: 600;
            margin: 0;
        }
        
        .demo-subtitle {
            font-size: 14px;
            opacity: 0.9;
            margin: 8px 0 0 0;
        }
        
        .feature-section {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-section:last-child {
            border-bottom: none;
        }
        
        .feature-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-panel {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            border: 1px solid #e9ecef;
        }
        
        .demo-row {
            display: flex;
            gap: 12px;
            margin-bottom: 12px;
        }
        
        .demo-row:last-child {
            margin-bottom: 0;
        }
        
        .demo-item {
            flex: 1;
        }
        
        .demo-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .demo-select {
            width: 100%;
            height: 36px;
            padding: 0 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: white;
            font-size: 14px;
            color: #333;
            outline: none;
            cursor: pointer;
        }
        
        .demo-select:disabled {
            background-color: #f8f9fa;
            color: #999;
            cursor: not-allowed;
            border-color: #e9ecef;
        }
        
        .demo-select:focus {
            border-color: #c00714;
        }
        
        .flow-diagram {
            background: #fff;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
            text-align: center;
        }
        
        .flow-step {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            margin: 4px;
        }
        
        .flow-arrow {
            color: #c00714;
            font-size: 16px;
            margin: 0 8px;
        }
        
        .improvement-list {
            list-style: none;
            padding: 0;
            margin: 12px 0;
        }
        
        .improvement-list li {
            padding: 6px 0;
            font-size: 14px;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .improvement-list li:before {
            content: "✅";
            font-size: 14px;
        }
        
        .demo-actions {
            padding: 20px;
            background: #f8f9fa;
            text-align: center;
        }
        
        .demo-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        
        .demo-btn:hover {
            background: #a00610;
            transform: translateY(-1px);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }
        
        .highlight-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .highlight-text {
            font-size: 14px;
            color: #856404;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🔗 课程章节联动筛选</h1>
            <p class="demo-subtitle">智能联动，精确筛选课程内容</p>
        </div>
        
        <div class="feature-section">
            <div class="feature-title">
                🎯 筛选面板演示
            </div>
            <div class="demo-panel">
                <div class="demo-row">
                    <div class="demo-item">
                        <label class="demo-label">课程分类</label>
                        <select class="demo-select" id="demoCoursSelect">
                            <option value="all">全部课程</option>
                            <option value="course1">思想道德与法治</option>
                            <option value="course2">中国近现代史纲要</option>
                            <option value="course3">马克思主义基本原理</option>
                            <option value="course4">毛泽东思想概论</option>
                        </select>
                    </div>
                    <div class="demo-item">
                        <label class="demo-label">章节分类</label>
                        <select class="demo-select" id="demoSectionSelect" disabled>
                            <option value="all">全部章节</option>
                        </select>
                    </div>
                </div>
                <div class="demo-row">
                    <div class="demo-item">
                        <label class="demo-label">排序方式</label>
                        <select class="demo-select">
                            <option value="createTime-desc">最新发布</option>
                            <option value="view-desc">热门推荐</option>
                            <option value="rating-desc">评分最高</option>
                        </select>
                    </div>
                    <div class="demo-item">
                        <label class="demo-label">资源类型</label>
                        <select class="demo-select">
                            <option value="all">全部类型</option>
                            <option value="video">视频课程</option>
                            <option value="document">文档资料</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="feature-section">
            <div class="feature-title">
                🔄 联动流程
            </div>
            <div class="flow-diagram">
                <span class="flow-step">选择课程</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">自动加载章节</span>
                <span class="flow-arrow">→</span>
                <span class="flow-step">精确筛选</span>
            </div>
            <ul class="improvement-list">
                <li>选择具体课程后，章节选择器自动启用</li>
                <li>根据课程ID动态加载对应的章节列表</li>
                <li>支持多级章节结构的层次显示</li>
                <li>实时提示当前选择状态</li>
            </ul>
        </div>
        
        <div class="feature-section">
            <div class="feature-title">
                ⚡ 核心功能
            </div>
            <ul class="improvement-list">
                <li>课程选择器：显示所有可用课程</li>
                <li>章节联动：根据课程自动加载章节</li>
                <li>智能禁用：未选择课程时章节选择器禁用</li>
                <li>状态提示：选择操作的即时反馈</li>
                <li>数据缓存：优化加载性能</li>
                <li>错误处理：网络异常时的降级处理</li>
            </ul>
        </div>
        
        <div class="highlight-box">
            <div class="highlight-title">
                💡 使用说明
            </div>
            <div class="highlight-text">
                1. 首先选择具体的课程，章节选择器将自动启用<br>
                2. 选择课程后，系统自动加载该课程的章节列表<br>
                3. 可进一步选择具体章节进行精确筛选<br>
                4. 配合排序和类型筛选实现多维度筛选
            </div>
        </div>
        
        <div class="demo-actions">
            <a href="pages/course-list.html" class="demo-btn">🚀 体验联动筛选功能</a>
        </div>
    </div>
    
    <script>
        // 演示联动功能
        const demoCoursSelect = document.getElementById('demoCoursSelect');
        const demoSectionSelect = document.getElementById('demoSectionSelect');
        
        const courseSections = {
            'course1': [
                { id: 'section1-1', name: '第一章 人生的青春之问' },
                { id: 'section1-2', name: '第二章 坚定理想信念' },
                { id: 'section1-3', name: '第三章 弘扬中国精神' }
            ],
            'course2': [
                { id: 'section2-1', name: '第一章 反对外国侵略的斗争' },
                { id: 'section2-2', name: '第二章 对国家出路的早期探索' },
                { id: 'section2-3', name: '第三章 辛亥革命与君主专制制度的终结' }
            ],
            'course3': [
                { id: 'section3-1', name: '第一章 马克思主义是关于无产阶级和人类解放的科学' },
                { id: 'section3-2', name: '第二章 世界的物质性及发展规律' },
                { id: 'section3-3', name: '第三章 实践与认识及其发展规律' }
            ]
        };
        
        demoCoursSelect.addEventListener('change', function() {
            const selectedCourse = this.value;
            
            if (selectedCourse === 'all') {
                demoSectionSelect.disabled = true;
                demoSectionSelect.innerHTML = '<option value="all">全部章节</option>';
            } else {
                demoSectionSelect.disabled = false;
                let optionsHtml = '<option value="all">全部章节</option>';
                
                const sections = courseSections[selectedCourse] || [];
                sections.forEach(section => {
                    optionsHtml += `<option value="${section.id}">${section.name}</option>`;
                });
                
                demoSectionSelect.innerHTML = optionsHtml;
            }
        });
    </script>
</body>
</html>
