<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>发布心声 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        .release-container {
            padding: 16px;
            background: #f8f9fa;
            min-height: calc(100vh - 140px);
            margin-top: 56px;
        }

        .release-form {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            font-size: 14px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #c00714;
            background: white;
            box-shadow: 0 0 0 3px rgba(192, 7, 20, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .category-select {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-top: 8px;
        }

        .category-option {
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            font-weight: 500;
        }

        .category-option.active {
            border-color: #c00714;
            background: rgba(192, 7, 20, 0.1);
            color: #c00714;
        }

        .submit-btn {
            width: 100%;
            padding: 16px;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .submit-btn:active {
            transform: scale(0.98);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 20px;
            font-size: 13px;
            color: #856404;
        }

        .tips-icon {
            display: inline-block;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="header-title">发布心声</h1>
            <button class="menu-btn" id="menuBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 18H21V16H3V18ZM3 13H21V11H3V13ZM3 6V8H21V6H3Z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 主要内容 -->
    <main class="release-container">
        <div class="tips">
            <span class="tips-icon">💡</span>
            分享你的学习心得、感悟或建议，与大家一起交流成长！
        </div>

        <form class="release-form" id="releaseForm">
            <div class="form-group">
                <label class="form-label">标题</label>
                <input type="text" class="form-input" id="titleInput" placeholder="请输入标题..." maxlength="50" required>
            </div>

            <div class="form-group">
                <label class="form-label">分类</label>
                <div class="category-select">
                    <div class="category-option active" data-category="学习心得">学习心得</div>
                    <div class="category-option" data-category="思想感悟">思想感悟</div>
                    <div class="category-option" data-category="建议反馈">建议反馈</div>
                    <div class="category-option" data-category="其他分享">其他分享</div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">内容</label>
                <textarea class="form-input form-textarea" id="contentInput" placeholder="分享你的想法..." maxlength="1000" required></textarea>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                发布心声
            </button>
        </form>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <script>
        let selectedCategory = '学习心得';

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 初始化表单
            initForm();
        });

        function initForm() {
            // 分类选择
            const categoryOptions = document.querySelectorAll('.category-option');
            categoryOptions.forEach(option => {
                option.addEventListener('click', function() {
                    categoryOptions.forEach(opt => opt.classList.remove('active'));
                    this.classList.add('active');
                    selectedCategory = this.getAttribute('data-category');
                });
            });

            // 表单提交
            const form = document.getElementById('releaseForm');
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                submitVoice();
            });
        }

        function submitVoice() {
            const title = document.getElementById('titleInput').value.trim();
            const content = document.getElementById('contentInput').value.trim();
            const submitBtn = document.getElementById('submitBtn');

            if (!title) {
                MobileUtils.showToast('请输入标题', 'error');
                return;
            }

            if (!content) {
                MobileUtils.showToast('请输入内容', 'error');
                return;
            }

            // 检查登录状态
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast('请先登录', 'error');
                setTimeout(() => {
                    window.location.href = '../login.html';
                }, 1500);
                return;
            }

            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '发布中...';

            // 提交数据
            $.ajax({
                url: baseurl + "/posts",
                type: 'POST',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: JSON.stringify({
                    title: title,
                    content: content,
                    category: selectedCategory,
                    type: 'voice'
                }),
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        MobileUtils.showToast('发布成功！', 'success');
                        setTimeout(() => {
                            window.location.href = 'community.html';
                        }, 1500);
                    } else {
                        MobileUtils.showToast(res.message || '发布失败', 'error');
                        resetSubmitBtn();
                    }
                },
                error: (err) => {
                    console.error('发布失败:', err);
                    MobileUtils.showToast('发布失败，请稍后重试', 'error');
                    resetSubmitBtn();
                }
            });
        }

        function resetSubmitBtn() {
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = false;
            submitBtn.textContent = '发布心声';
        }
    </script>
</body>
</html>
