<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>党建学习 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
</head>
<body class="mobile-party-study">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
    </div>

    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M20 11H7.83L13.42 5.41L12 4L4 12L12 20L13.41 18.59L7.83 13H20V11Z"/>
                </svg>
            </button>
            <h1 class="page-title">党建学习</h1>
            <button class="search-btn" id="searchBtn">
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索党建学习内容..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 分类筛选 -->
        <section class="filter-section">
            <div class="container">
                <div class="filter-tabs" id="filterTabs">
                    <button class="filter-tab active" data-category="all">全部</button>
                    <button class="filter-tab" data-category="theory">理论学习</button>
                    <button class="filter-tab" data-category="policy">政策解读</button>
                    <button class="filter-tab" data-category="activity">党建活动</button>
                    <button class="filter-tab" data-category="history">党史学习</button>
                </div>
            </div>
        </section>

        <!-- 党建学习内容列表 -->
        <section class="content-section">
            <div class="container">
                <div class="content-list" id="contentList">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
                
                <!-- 加载更多按钮 -->
                <div class="load-more-wrapper" id="loadMoreWrapper">
                    <button class="load-more-btn" id="loadMoreBtn">加载更多</button>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        // 页面变量
        let currentPage = 1;
        let currentCategory = 'all';
        let isLoading = false;
        let hasMore = true;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initMobileFeatures();
            
            // 初始化页面功能
            initPartyStudy();
            
            // 隐藏加载动画
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1000);
        });

        function initMobileFeatures() {
            // 初始化触摸交互
            initTouchInteractions();
            
            // 初始化导航
            initNavigation();
            
            // 初始化搜索
            initSearch();
            
            // 初始化返回顶部
            initBackToTop();
        }

        function initPartyStudy() {
            // 初始化分类筛选
            initCategoryFilter();
            
            // 初始化加载更多
            initLoadMore();
            
            // 加载初始数据
            loadPartyStudyContent();
        }

        function initCategoryFilter() {
            const filterTabs = document.querySelectorAll('.filter-tab');
            
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // 更新激活状态
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 重置分页
                    currentPage = 1;
                    currentCategory = category;
                    hasMore = true;
                    
                    // 重新加载数据
                    loadPartyStudyContent(true);
                });
            });
        }

        function initLoadMore() {
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            
            if (loadMoreBtn) {
                loadMoreBtn.addEventListener('click', function() {
                    if (!isLoading && hasMore) {
                        currentPage++;
                        loadPartyStudyContent();
                    }
                });
            }
        }

        function loadPartyStudyContent(reset = false) {
            if (isLoading) return;
            
            isLoading = true;
            const contentList = document.getElementById('contentList');
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            
            // 显示加载状态
            if (reset) {
                contentList.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';
            } else {
                loadMoreBtn.textContent = '加载中...';
                loadMoreBtn.disabled = true;
            }

            // 获取党建学习数据
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: currentPage,
                    pageSize: 10,
                    categoryId: getPartyStudyCategoryId(currentCategory)
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderPartyStudyContent(res.data.list || [], reset);
                        
                        // 更新分页状态
                        hasMore = res.data.list && res.data.list.length === 10;
                        updateLoadMoreButton();
                    } else {
                        if (reset) {
                            contentList.innerHTML = '<div class="empty-state">暂无党建学习内容</div>';
                        }
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载党建学习内容失败:', err);
                    if (reset) {
                        contentList.innerHTML = '<div class="empty-state">加载失败，请稍后重试</div>';
                    }
                    isLoading = false;
                    updateLoadMoreButton();
                }
            });
        }

        function getPartyStudyCategoryId(category) {
            // 根据分类返回对应的分类ID
            const categoryMap = {
                'all': null,
                'theory': sessionStorage.getItem('theoryClassId'),
                'policy': sessionStorage.getItem('policyClassId'),
                'activity': sessionStorage.getItem('activityClassId'),
                'history': sessionStorage.getItem('historyClassId')
            };
            
            return categoryMap[category] || null;
        }

        function renderPartyStudyContent(data, reset = false) {
            const contentList = document.getElementById('contentList');
            
            if (reset) {
                contentList.innerHTML = '';
            }
            
            if (!data || data.length === 0) {
                if (reset) {
                    contentList.innerHTML = '<div class="empty-state">暂无党建学习内容</div>';
                }
                return;
            }

            let html = '';
            data.forEach(item => {
                html += `
                    <div class="content-item" data-id="${item.id}">
                        <div class="content-header">
                            <h3 class="content-title">${item.title || '无标题'}</h3>
                            <span class="content-category">${getCategoryName(item.categoryName)}</span>
                        </div>
                        <div class="content-summary">${item.summary || item.content || '暂无摘要'}</div>
                        <div class="content-meta">
                            <span class="content-time">${MobileUtils.formatTime(item.createTime || item.publishedTime)}</span>
                            <span class="content-views">阅读 ${item.clickNum || 0}</span>
                        </div>
                    </div>
                `;
            });

            if (reset) {
                contentList.innerHTML = html;
            } else {
                contentList.insertAdjacentHTML('beforeend', html);
            }

            // 绑定点击事件
            bindContentEvents();
        }

        function getCategoryName(categoryName) {
            if (!categoryName) return '党建学习';
            
            const categoryMap = {
                'theory': '理论学习',
                'policy': '政策解读',
                'activity': '党建活动',
                'history': '党史学习'
            };
            
            return categoryMap[categoryName] || categoryName;
        }

        function bindContentEvents() {
            const contentItems = document.querySelectorAll('.content-item');
            
            contentItems.forEach(item => {
                item.addEventListener('click', function() {
                    const id = this.getAttribute('data-id');
                    if (id) {
                        // 记录点击统计
                        clicknum(id);
                        
                        // 跳转到详情页面
                        window.location.href = `party-study-detail.html?id=${id}`;
                    }
                });
            });
        }

        function updateLoadMoreButton() {
            const loadMoreBtn = document.getElementById('loadMoreBtn');
            const loadMoreWrapper = document.getElementById('loadMoreWrapper');
            
            if (loadMoreBtn && loadMoreWrapper) {
                if (hasMore) {
                    loadMoreWrapper.style.display = 'block';
                    loadMoreBtn.textContent = '加载更多';
                    loadMoreBtn.disabled = false;
                } else {
                    loadMoreWrapper.style.display = 'none';
                }
            }
        }
    </script>
</body>
</html>
