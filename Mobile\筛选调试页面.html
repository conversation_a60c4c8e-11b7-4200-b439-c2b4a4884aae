<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>筛选功能调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .debug-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .debug-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .debug-section {
            margin-bottom: 30px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #c00714;
        }
        
        .debug-section h3 {
            margin: 0 0 12px 0;
            font-size: 16px;
            color: #333;
        }
        
        .debug-steps {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .debug-steps li {
            padding: 8px 0;
            font-size: 14px;
            color: #666;
            border-bottom: 1px solid #e9ecef;
        }
        
        .debug-steps li:last-child {
            border-bottom: none;
        }
        
        .debug-steps li:before {
            content: "🔍 ";
            margin-right: 8px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            margin: 12px 0;
            overflow-x: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .debug-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin: 8px 8px 8px 0;
        }
        
        .debug-btn:hover {
            background: #a00610;
        }
        
        .console-output {
            background: #1e1e1e;
            color: #d4d4d4;
            border-radius: 6px;
            padding: 12px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 12px 0;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 12px;
            border-radius: 6px;
            border: 1px solid #ffeaa7;
            margin: 12px 0;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <div class="debug-title">🐛 筛选功能调试指南</div>
        
        <div class="debug-section">
            <h3>📋 问题诊断步骤</h3>
            <ol class="debug-steps">
                <li>打开浏览器开发者工具（F12）</li>
                <li>切换到 Console（控制台）标签页</li>
                <li>访问课程列表页面</li>
                <li>观察控制台输出的调试信息</li>
                <li>尝试使用筛选功能</li>
                <li>检查筛选过程中的日志输出</li>
            </ol>
        </div>
        
        <div class="debug-section">
            <h3>🔍 关键检查点</h3>
            
            <div class="highlight">
                <strong>1. 数据加载检查</strong>
                <div class="code-block">
// 在控制台中查找这些日志：
"课程数据API响应:" - API返回的原始数据
"原始课程数据:" - 从API获取的课程列表
"处理后的课程数据:" - 经过处理的课程数据
"课程选择器已更新，共加载 X 个课程" - 课程选择器更新状态
                </div>
            </div>
            
            <div class="highlight">
                <strong>2. 筛选过程检查</strong>
                <div class="code-block">
// 在控制台中查找这些日志：
"开始筛选课程，原始数据数量:" - 开始筛选时的数据量
"当前筛选条件:" - 当前设置的筛选条件
"第一个课程的匹配情况:" - 详细的匹配过程
"筛选后的课程数量:" - 筛选结果数量
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>⚠️ 常见问题及解决方案</h3>
            
            <div style="margin-bottom: 16px;">
                <span class="status-indicator status-error"></span>
                <strong>问题1：筛选后没有结果</strong>
                <div style="margin-left: 20px; margin-top: 8px; color: #666;">
                    <strong>可能原因：</strong>
                    <ul>
                        <li>筛选条件过于严格，没有匹配的课程</li>
                        <li>课程数据字段与筛选逻辑不匹配</li>
                        <li>API返回的数据结构发生变化</li>
                    </ul>
                    <strong>解决方法：</strong>
                    <ul>
                        <li>检查控制台中的筛选条件和匹配情况</li>
                        <li>尝试重置筛选条件</li>
                        <li>检查课程数据的字段结构</li>
                    </ul>
                </div>
            </div>
            
            <div style="margin-bottom: 16px;">
                <span class="status-indicator status-warning"></span>
                <strong>问题2：课程选择器为空</strong>
                <div style="margin-left: 20px; margin-top: 8px; color: #666;">
                    <strong>可能原因：</strong>
                    <ul>
                        <li>/project/section/list/tree API返回空数据</li>
                        <li>数据提取逻辑有误</li>
                        <li>网络请求失败</li>
                    </ul>
                    <strong>解决方法：</strong>
                    <ul>
                        <li>检查API响应数据</li>
                        <li>验证数据提取逻辑</li>
                        <li>使用默认数据进行测试</li>
                    </ul>
                </div>
            </div>
            
            <div style="margin-bottom: 16px;">
                <span class="status-indicator status-success"></span>
                <strong>问题3：章节联动不工作</strong>
                <div style="margin-left: 20px; margin-top: 8px; color: #666;">
                    <strong>可能原因：</strong>
                    <ul>
                        <li>课程数据中缺少children字段</li>
                        <li>章节数据结构不正确</li>
                        <li>事件绑定失败</li>
                    </ul>
                    <strong>解决方法：</strong>
                    <ul>
                        <li>检查课程数据的children字段</li>
                        <li>验证章节数据结构</li>
                        <li>检查事件绑定是否成功</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="debug-section">
            <h3>🛠️ 调试工具</h3>
            <button class="debug-btn" onclick="openConsole()">打开控制台</button>
            <button class="debug-btn" onclick="checkLocalStorage()">检查本地存储</button>
            <button class="debug-btn" onclick="testAPI()">测试API连接</button>
            <button class="debug-btn" onclick="clearCache()">清除缓存</button>
        </div>
        
        <div class="debug-section">
            <h3>📞 获取帮助</h3>
            <p>如果按照上述步骤仍无法解决问题，请：</p>
            <ol>
                <li>截图控制台中的错误信息</li>
                <li>记录具体的操作步骤</li>
                <li>提供筛选条件的详细信息</li>
                <li>联系技术支持团队</li>
            </ol>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="pages/course-list.html" class="debug-btn">🚀 返回课程列表页面</a>
        </div>
    </div>
    
    <script>
        function openConsole() {
            alert('请按 F12 键打开开发者工具，然后切换到 Console 标签页');
        }
        
        function checkLocalStorage() {
            const header = sessionStorage.getItem("header");
            const baseurl = localStorage.getItem("baseurl") || sessionStorage.getItem("baseurl");
            
            console.log('认证信息:', header ? '已设置' : '未设置');
            console.log('API地址:', baseurl || '未设置');
            
            if (!header) {
                alert('⚠️ 未找到认证信息，请先登录');
            } else if (!baseurl) {
                alert('⚠️ 未找到API地址配置');
            } else {
                alert('✅ 本地存储检查通过');
            }
        }
        
        function testAPI() {
            const baseurl = localStorage.getItem("baseurl") || sessionStorage.getItem("baseurl") || "http://localhost:5500/api";
            const header = sessionStorage.getItem("header");
            
            if (!header) {
                alert('请先登录获取认证信息');
                return;
            }
            
            console.log('测试API连接...');
            
            fetch(baseurl + "/project/section/list/tree", {
                headers: {
                    "Authorization": header
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('API测试结果:', data);
                if (data.code === '200') {
                    alert('✅ API连接正常');
                } else {
                    alert('⚠️ API返回错误: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                console.error('API测试失败:', error);
                alert('❌ API连接失败: ' + error.message);
            });
        }
        
        function clearCache() {
            if (confirm('确定要清除浏览器缓存吗？这将刷新页面。')) {
                localStorage.clear();
                sessionStorage.clear();
                location.reload();
            }
        }
    </script>
</body>
</html>
