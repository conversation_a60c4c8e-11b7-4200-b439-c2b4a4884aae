<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>学习模块 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 学习模块页面专用样式 */
        .modules-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .modules-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .modules-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .modules-content {
            padding: 16px;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .module-card {
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .module-card:active {
            transform: translateY(2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .module-header {
            padding: 20px;
            background: linear-gradient(135deg, var(--module-color-1), var(--module-color-2));
            color: white;
            position: relative;
            overflow: hidden;
        }
        
        .module-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: rotate(45deg);
        }
        
        .module-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            position: relative;
            z-index: 1;
        }
        
        .module-icon svg {
            width: 24px;
            height: 24px;
        }
        
        .module-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 4px;
            position: relative;
            z-index: 1;
        }
        
        .module-description {
            font-size: 14px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .module-body {
            padding: 20px;
        }
        
        .module-stats {
            display: flex;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #999;
        }
        
        .module-features {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .feature-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        
        .module-arrow {
            position: absolute;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        /* 不同模块的颜色主题 */
        .module-card:nth-child(1) {
            --module-color-1: #c00714;
            --module-color-2: #a00610;
        }
        
        .module-card:nth-child(2) {
            --module-color-1: #43e97b;
            --module-color-2: #38d9a9;
        }
        
        .module-card:nth-child(3) {
            --module-color-1: #667eea;
            --module-color-2: #764ba2;
        }
        
        .module-card:nth-child(4) {
            --module-color-1: #f093fb;
            --module-color-2: #f5576c;
        }
        
        .module-card:nth-child(5) {
            --module-color-1: #fa709a;
            --module-color-2: #fee140;
        }
        
        .module-card:nth-child(6) {
            --module-color-1: #4facfe;
            --module-color-2: #00f2fe;
        }
        
        @media (min-width: 768px) {
            .modules-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 375px) {
            .modules-header {
                padding: 16px 12px;
            }
            
            .modules-content {
                padding: 12px;
            }
            
            .modules-grid {
                gap: 12px;
            }
            
            .module-header {
                padding: 16px;
            }
            
            .module-body {
                padding: 16px;
            }
        }
    </style>
</head>
<body class="mobile-modules">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">学习模块</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 模块头部 -->
    <section class="modules-header">
        <div class="modules-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3ZM5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z"/>
            </svg>
            学习模块
        </div>
        <div class="modules-subtitle">多元化学习体验，全方位能力提升</div>
    </section>

    <!-- 学习模块 -->
    <main class="modules-content">
        <div class="modules-grid">
            <!-- 课程学习 -->
            <a href="course-list.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM18 14H6V6H18V14ZM8 15.5H16V17.5H8V15.5Z"/>
                        </svg>
                    </div>
                    <div class="module-name">课程学习</div>
                    <div class="module-description">系统化学习，全面提升能力</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">180+</div>
                            <div class="stat-label">精品课程</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">课程分类</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">3000+</div>
                            <div class="stat-label">学习人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">视频教学</span>
                        <span class="feature-tag">在线答题</span>
                        <span class="feature-tag">学习证书</span>
                    </div>
                </div>
            </a>

            <!-- 红色书籍 -->
            <a href="redbooks.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V4C20 2.9 19.1 2 18 2ZM18 20H6V4H7V13L9.5 11.5L12 13V4H18V20Z"/>
                        </svg>
                    </div>
                    <div class="module-name">红色书籍</div>
                    <div class="module-description">传承红色基因，汲取精神力量</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">200+</div>
                            <div class="stat-label">经典书籍</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">分类目录</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1000+</div>
                            <div class="stat-label">学习人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">在线阅读</span>
                        <span class="feature-tag">PDF下载</span>
                        <span class="feature-tag">学习记录</span>
                    </div>
                </div>
            </a>

            <!-- VR红色游学 -->
            <a href="vrtour.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z"/>
                            <circle cx="10" cy="12" r="1.5"/>
                            <circle cx="14" cy="12" r="1.5"/>
                        </svg>
                    </div>
                    <div class="module-name">VR红色游学</div>
                    <div class="module-description">沉浸式红色文化体验</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">30+</div>
                            <div class="stat-label">VR场景</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">红色基地</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">500+</div>
                            <div class="stat-label">体验人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">VR体验</span>
                        <span class="feature-tag">360°全景</span>
                        <span class="feature-tag">互动学习</span>
                    </div>
                </div>
            </a>

            <!-- 虚仿实验空间 -->
            <a href="experiment.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 2V7H7.5C6.67 7 6 7.67 6 8.5V10H4.5C3.67 10 3 10.67 3 11.5V20.5C3 21.33 3.67 22 4.5 22H19.5C20.33 22 21 21.33 21 20.5V11.5C21 10.67 20.33 10 19.5 10H18V8.5C18 7.67 17.33 7 16.5 7H15V2H9ZM11 4H13V7H11V4ZM8 9H16V10H8V9ZM5 12H19V20H5V12ZM7 14V18H9V14H7ZM11 14V18H13V14H11ZM15 14V18H17V14H15Z"/>
                        </svg>
                    </div>
                    <div class="module-name">虚仿实验空间</div>
                    <div class="module-description">沉浸式虚拟实验体验</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">25+</div>
                            <div class="stat-label">实验项目</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">8+</div>
                            <div class="stat-label">学科领域</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">300+</div>
                            <div class="stat-label">实验人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">虚拟实验</span>
                        <span class="feature-tag">安全操作</span>
                        <span class="feature-tag">实时反馈</span>
                    </div>
                </div>
            </a>

            <!-- 医德博物馆 -->
            <a href="museum.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z"/>
                        </svg>
                    </div>
                    <div class="module-name">医德博物馆</div>
                    <div class="module-description">传承医者仁心，弘扬医德精神</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">展览主题</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100+</div>
                            <div class="stat-label">珍贵文物</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">800+</div>
                            <div class="stat-label">参观人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">虚拟展览</span>
                        <span class="feature-tag">文物介绍</span>
                        <span class="feature-tag">历史故事</span>
                    </div>
                </div>
            </a>

            <!-- 总书记的足迹 -->
            <a href="footprint.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                        </svg>
                    </div>
                    <div class="module-name">总书记的足迹</div>
                    <div class="module-description">追寻领袖足迹，感悟思想伟力</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">200+</div>
                            <div class="stat-label">足迹记录</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">31+</div>
                            <div class="stat-label">省市地区</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">1200+</div>
                            <div class="stat-label">学习人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">地图展示</span>
                        <span class="feature-tag">时间轴</span>
                        <span class="feature-tag">详细记录</span>
                    </div>
                </div>
            </a>

            <!-- 建党学习 -->
            <a href="party-study.html" class="module-card">
                <div class="module-header">
                    <div class="module-icon">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                        </svg>
                    </div>
                    <div class="module-name">建党学习</div>
                    <div class="module-description">学习党史党建，传承红色基因</div>
                    <div class="module-arrow">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </div>
                </div>
                <div class="module-body">
                    <div class="module-stats">
                        <div class="stat-item">
                            <div class="stat-number">50+</div>
                            <div class="stat-label">党建课程</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">专题学习</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">800+</div>
                            <div class="stat-label">学习人次</div>
                        </div>
                    </div>
                    <div class="module-features">
                        <span class="feature-tag">党史学习</span>
                        <span class="feature-tag">理论教育</span>
                        <span class="feature-tag">实践活动</span>
                    </div>
                </div>
            </a>

        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="course-list.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>个人中心</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
        });
    </script>
</body>
</html>
